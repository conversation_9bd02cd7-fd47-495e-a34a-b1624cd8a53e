package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksDtl
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.logs.DiscodeService
import grails.transaction.Transactional

class IbookgptController {
    def redisService
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    DiscodeService discodeService

    def index() {
        if(params.siteName==null) params.siteName = "ibookgpt"
        userManagementService.setUserSession(params.siteName, session, servletContext, response)

        // Handle discount code logic
        def discountData = handleDiscountCode(params.disCode, "index")

        [showDiscountPopup: discountData.showPopup, discountCode: discountData.code, discountName: discountData.discountName]
    }
    def features(){}
    def howItWorks(){}
    def contact(){}
    def benefits(){}
    def faq(){}
    def printBookBundling(){
        [title:"Print Book Bundling - iBookGPT"]
    }

    @Transactional
    def istore(){
        println("istore")
        if(params.siteName==null) params.siteName = "ibookgpt"
        userManagementService.setUserSession(params.siteName, session, servletContext, response)

        // Handle discount code logic
        def discountData = handleDiscountCode(params.disCode, "istore")

        [title:"iStore - Choose your AI Tutor", showDiscountPopup: discountData.showPopup, discountCode: discountData.code, discountName: discountData.discountName]
    }

    @Transactional
    def ibookDtl() {
        if(params.siteName==null) params.siteName = "ibookgpt"
        userManagementService.setUserSession(params.siteName, session, servletContext, response)

        // Get book details using the same logic as aiBookDtl
        def bookId = params.id
        if (!bookId) {
            flash.error = "Book ID is required"
            redirect(action: "istore")
            return
        }

        try {
            // Get book details from dataProviderService
            def booksMst = dataProviderService.getBooksMst(new Long(bookId))
            if (!booksMst) {
                flash.error = "Book not found"
                redirect(action: "istore")
                return
            }

            // Get pricing information
            def bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Integer(bookId), "bookGPT")
            def price = bookPriceDtl?.sellPrice ?: 0
            def listPrice = bookPriceDtl?.listPrice ?: price

            // Get package book details if available
            def packageBooks = []
            if (booksMst.packageBookIds) {
                def packageBookIds = booksMst.packageBookIds.split(",")
                packageBookIds.each { id ->
                    def trimmedId = id.trim()
                    if (trimmedId) {
                        try {
                            def packageBook = dataProviderService.getBooksMst(new Long(trimmedId))
                            if (packageBook) {
                                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(new Long(trimmedId))
                                packageBooks.add([
                                    id: packageBook.id,
                                    title: packageBook.title,
                                    subject: booksTagDtl?.subject ?: "",
                                    coverImage: null,
                                    icon: getRandomSubjectIcon(packageBook.id),
                                    coverColor: getRandomCoverColor(packageBook.id)
                                ])
                            }
                        } catch (Exception e) {
                            // Skip invalid book IDs
                            println("Error loading package book ${trimmedId}: ${e.message}")
                        }
                    }
                }
            }

            // Handle discount code logic
            def discountData = handleDiscountCode(params.disCode, "ibookDtl")

            [
                title: "Book Details - iBookGPT",
                bookDetails: [
                    id: booksMst.id,
                    title: booksMst.title,
                    description: booksMst.description,
                    price: price,
                    coverImage: booksMst.coverImage,
                    authors: booksMst.authors,
                    packageBookIds: booksMst.packageBookIds,
                    packageBooks: packageBooks,
                    coverColor: getRandomCoverColor(booksMst.id),
                    listPrice: listPrice
                ],
                showDiscountPopup: discountData.showPopup,
                discountCode: discountData.code,
                discountName: discountData.discountName
            ]
        } catch (Exception e) {
            println("Error in ibookDtl: ${e.message}")
            flash.error = "Error loading book details"
            redirect(action: "istore")
            return
        }
    }

    /**
     * Handle discount code logic - check if should show popup and track display
     */
    private Map handleDiscountCode(String disCode, String pageName) {
        boolean showPopup = false
        String code = null
        String discountName = null

        if (disCode && !session.dCodeAlreadyShown) {
            showPopup = true
            code = disCode
            session.dCodeAlreadyShown = true

            // Get discount name from DiscountMst table
            try {
                Integer siteId = getSiteId(request)
                discountName = discodeService.getDiscountNameByCouponCodeAndSiteId(disCode, siteId)
            } catch (Exception e) {
                log.error("Error getting discount name: " + e.getMessage(), e)
            }

            // Track the discount code display
            try {
                String sessionId = session.id
                Integer siteId = getSiteId(request)
                String username = springSecurityService.currentUser?.username
                String ipAddress = getClientIpAddress(request)
                String userAgent = request.getHeader("User-Agent")

                discodeService.trackDiscountCodeDisplay(disCode, username, sessionId, siteId, ipAddress, userAgent, pageName)
            } catch (Exception e) {
                log.error("Error tracking discount code display: " + e.getMessage(), e)
            }
        }

        return [showPopup: showPopup, code: code, discountName: discountName]
    }

    private Integer getSiteId(request) {
        try {
            return session['siteId'] as Integer ?: 1
        } catch (Exception e) {
            return 1
        }
    }

    private String getClientIpAddress(request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For")
        if (xForwardedFor && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim()
        }

        String xRealIp = request.getHeader("X-Real-IP")
        if (xRealIp && !xRealIp.isEmpty()) {
            return xRealIp
        }

        return request.remoteAddr
    }

    private String extractSubjectFromTitle(String title) {
        def subjects = ["Mathematics", "Math", "Science", "Physics", "Chemistry", "Biology",
                       "English", "Hindi", "Social Studies", "History", "Geography", "Economics"]

        for (subject in subjects) {
            if (title.toLowerCase().contains(subject.toLowerCase())) {
                return subject
            }
        }

        // If no subject found, use first two words of title
        def words = title.split(" ")
        return words.length >= 2 ? "${words[0]} ${words[1]}" : (words[0] ?: "Subject")
    }

    private String getRandomSubjectIcon(Long bookId) {
        def icons = ["📐", "🔬", "⚛️", "🧪", "🧬", "📚", "🇮🇳", "🌍", "📜", "🗺️", "💰", "📖", "🎯", "🎨", "🎵", "⚽", "🏆", "🌟"]

        // Use book ID for deterministic "random" selection so same book always gets same icon
        def index = (bookId % icons.size()) as int
        return icons[index]
    }

    private String getRandomCoverColor(Long bookId) {
        def colors = ["#FF5722", "#2196F3", "#3F51B5", "#E91E63", "#009688", "#FF9800", "#4CAF50", "#9C27B0", "#F44336", "#607D8B", "#795548", "#FFC107"]

        // Use book ID for deterministic "random" selection so same book always gets same color
        def index = (bookId % colors.size()) as int
        return colors[index]
    }
}
