<% if ("1".equals("" + session["siteId"])) { %>
<asset:stylesheet href="wonderslate/signup.css" async="true"/>
<% } else { %>
<asset:stylesheet href="${session['entryController']}/signup.css" async="true"/>
<% } %>

<% String siteName = session.getAttribute("siteName") != null ? session.getAttribute("siteName") : grailsApplication.config.grails.appServer.siteName; %>

<%
    boolean mobileOnlyLogin = "mobile".equals("" + session.getAttribute("loginType"))
%>
<% if (!(("android".equals(session["appType"])) || ("ios".equals(session["appType"])))) { %>

<style>
/* Removing Input Field Arrows */
#loginOpen input::-webkit-outer-spin-button,
#loginOpen input::-webkit-inner-spin-button,
#signup input::-webkit-outer-spin-button,
#signup input::-webkit-inner-spin-button,
#forgotPasswordmodal input::-webkit-outer-spin-button,
#forgotPasswordmodal input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
#loginOpen input[type=number],
#signup input[type=number],
#forgotPasswordmodal input[type=number] {
    -moz-appearance: textfield;
}
.forgot,
.hide-password,
.newUserBtn,
.exUserBtn{
    background: transparent;
    border: none !important;
}
.newUserBtn,
.exUserBtn{
    font-weight: 700;
}
</style>

<!--------------    LOGIN MODAL  ------------->
<div class="modal fade" id="loginOpen" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
            <button type="button" class="close" data-dismiss="modal">X</button>
            <div class="modal-text-content col-lg-6 p-4 p-lg-5 d-flex">
                <img class="loginImg" src="${assetPath(src: 'wonderslate/login_img_one.webp')}" alt="Wonderslate login">
                <h1 id="loginMessage">The Joy of <span>Learning</span></h1>
            </div>
            <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5">
                <h4 class="modal-title head-title">Login</h4>
                <form class="mt-3 mb-4 text-center" method="post" action="/login/authenticate" name="signin" id="login-form">
                    <input type="hidden" name="username" value="">
                    <input class="form-control" placeholder="Mobile No./Email"
                           type=<%=mobileOnlyLogin ? "number" : "text"%> id="number" name="username_temp"
                           autocomplete="mobile-number" required onfocus="javascript:resetError();">
                    <p id="usernameError" class="text-left error-text"></p>
                    <div class="position-relative text-right mt-3">
                        <input class="form-control" placeholder="Password" type="password" id="password"
                               name="password" autocomplete="current-password" required onfocus="javascript:resetError();">
                        <button class="hide-password material-icons">visibility</button>
                        <p id="loginPasswordError" class="text-left error-text"></p>
                        <button onclick="forgotPasswordOpen()" class="forgot mt-2 d-inline-block">Forgot password?</button>
                    </div>

                    <p id="username-empty" class="error-msg mt-2"></p>
                    <p id="loginFailed" class="error-text mt-3" style="display:none;">Login failed. Please try again!</p>
                    <p class="error-text mt-3" id="limit-signin" style="display:none;"></p>

                    <input type="button" class="mt-4 btn login-btn" value="LOGIN" id="nrmlSignin"
                           onclick="submitSignIn();">

                </form>

                <div class="modal-footer justify-content-center border-0 pt-0">
                    <p>New User? <button onclick="signupModal()" class="newUserBtn">Sign-up</button></p>
                </div>

            </div>

            </div>

        </div>
    </div>
</div>

<!--------------    SIGNUP MODAL  ------------>
<div class="modal fade" id="signup" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
                <button type="button" class="close" data-dismiss="modal" id="closeButton">X</button>

                <div class="modal-text-content col-12 col-lg-6 p-4 p-lg-5 d-flex">
                    <img class="loginImg" src="${assetPath(src: 'wonderslate/login_img_one.webp')}" alt="Wonderslate login">
                    <h1 id="signupMessage">The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5">
                    <h4 class="modal-title head-title" id="signUpTitle">Sign-up</h4>
                    <p id="signUpSubTitle"></p>
                    <g:form name="adduser" url="[action: 'addUser', controller: 'creation']" method="post"
                            autocomplete="off" id="signInId" class="mt-3 mb-4">
                        <input type="text" class="form-control" placeholder="Name" id="name" name="name" required
                               value="">
                        <p id="nameError" class="text-left error-text"></p>
                        <input type="<%=mobileOnlyLogin ? "number" : "text"%>" name="migrateMobile" id="migrateMobile"
                               class="form-control mt-3" placeholder="Mobile No./Email" autocomplete="off"
                               required>
                        <p id="migrateMobileError" class="text-left error-text"></p>
                        <div class="position-relative mt-3">
                            <input type="password" class="form-control" placeholder="New password" id="signup-password"
                                   name="password" autocomplete="off" required>
                            <button class="hide-password material-icons">visibility</button>
                            <p id="signupPasswordError" class="text-left error-text"></p>
                        </div>
                        <input class="form-control mt-3" type="text" id="email" name="email" style="display: none;"
                               placeholder="Email" required>

                        <input type="number" class="form-control mt-3" placeholder="Enter OTP" name="signupOtp" id="signupOtp" required
                               maxlength="10" minlength="10" style="display: none;">
                        <p id="signupOTPError" class="text-left error-text"></p>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="otp_finished" value="true">
                        <input type="button" onclick="javascript:getOTPForSignup();" class="mt-4 btn login-btn"
                               value="GET OTP" id="migrateOtp">
                        <div class="verification-code mt-3" style="display: none;">

                            <input type="button" onclick="javascript:verifyMobileOTP();" class="mt-2 btn login-btn"
                                   value="Verify OTP" id="verifybtn" style="display: none">
                            <p class="mt-3">Verification code is sent to <b><span id="sentMobileNumbers"></span></b></p>

                            <div>
                                <span class="timer">
                                    <span class="time"></span>
                                </span>
                            </div>

                            <p class="resendotp mt-2" style="display: none;">Did not receive the OTP? <button
                                    onclick="resendOTP();">Resend</button></p>

                        </div>
                        <button class="mt-4 btn login-btn" type="button" onclick="formSubmit();"
                                style="display: none;" id="register">Register</button>
                    </g:form>
                    <div class="modal-footer justify-content-center border-0 pt-0">
                        <p>Already registered? <button onclick="loginOpen()" class="exUserBtn">Login</button></p>
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>

<!--------------    FORGOT-PASSWORD MODAL  ------------>
<div class="modal fade" id="forgotPasswordmodal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
                <button type="button" class="close" data-dismiss="modal">X</button>
                <div class="modal-text-content col-12 col-lg-6 p-4 p-lg-5 d-flex">
                    <img class="loginImg" src="${assetPath(src: 'wonderslate/login_img_one.webp')}" alt="Wonderslate login">
                    <h1>The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5">
                    <h4 class="modal-title head-title" id="forgotTitle">Forgot Password</h4>
                    <p id="forgotSubTitle">Don't worry. We'll fix this for you!</p>
                    <form class="mt-4 text-center" id="forgot-form">

                        <div class="otp-wrapper">

                            <input type="number" class="form-control mt-3" placeholder="Confirming OTP..." name="otpConfirm"
                                   id="otpConfirm" required maxlength="10" minlength="10">
                            <p id="ForgotPasswordOTPError" class="text-left error-text"></p>
                            <input type="button" onclick="javascript:verifyOTP();" class="mt-4 btn login-btn"
                                   value="Verify OTP">

                            <p class="mt-3">Verification code has been sent to <b><span id="sentMobileNumber"></span></b></p>
                            <div>
                                <span class="timer">
                                    <span class="time"></span>
                                </span>
                            </div><br>

                            <p class="resendotp" style="display: none;">Did not receive the OTP? <a
                                    href="javascript:resendForgotPasswordOTP();">Resend</a></p>

                        </div>

                        <p id="user-exist" class="error-msg"></p>

                        <span class="error-msg otperror-msg" style="display: none;">OTP failed. OTP is Incorrect.</span>

                        <p class="error-msg" id="limit-signin-verify"></p>

                    </form>
                    <g:form name="adduserFromForgot" url="[action: 'addUser', controller: 'creation']" method="post"
                            autocomplete="off" id="signUpFromForgot" class="mt-3 mb-4" style="display: none;">
                        <input type="text" class="form-control" placeholder="Name" id="nameFromForgot" name="name" required
                               value="" autocomplete="off">
                        <p id="nameErrorFromForgot" class="text-left error-text"></p>
                        <div class="position-relative mt-3">
                            <input type="password" class="form-control" placeholder="Password" id="passwordFromForgot"
                                   name="password" autocomplete="off" required>
                            <button class="hide-password material-icons">visibility</button>
                            <p id="passwordFromForgotError" class="text-left error-text"></p>
                        </div>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="email">
                        <input type="hidden" name="otp_finished" value="true">
                        <input type="button" onclick="javascript:formSubmitForgot();" class="mt-4 btn login-btn"
                               value="CONTINUE">
                    </g:form>
                </div>
            </div>

        </div>
    </div>
</div>

<% } %>

<% if(!"bookgpt".equals(params.action)){%>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" crossorigin="anonymous"></script>
<%}%>
<script>
    document.querySelector('.login_signup_loader').addEventListener('mdl-componentupgraded', function() {
        this.MaterialProgress.setProgress(44);
    });

    var functionNameToCall = "";
    var loginHappened = false;
    var siteIds = "<%=session.getAttribute("siteId")%>";
    var otpReg = "${otpReg}";
    var operation;
    var mobileNumber;
    var nextDestination = null;
    var userCheck = true;
    var validation;
    var forceRegisterMode=null;
    var registerBookId=null;
    var registerBookTitle=null;
    var returnFunctionCallForQuiz=null;


    //FORGOT PASSWORD SUBMISSION
    function formFPSubmit() {
        $("#emailidnf").hide();

        if (!$("#fPemail").val()) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {

            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos < 1 || dotpos < atpos + 2 || dotpos + 2 >= email.length) {
                return false;
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    //DISPLAYING FORGOT PASSWORD DETAILS
    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if ("OK" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-completed');
            $('#fp-user-email').html("“" + userEmail + "�?");
        } else if ("Google" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-google-paswd');
            $('#fp-user-email1').html("“" + userEmail + "�?");
        } else if ("Fail" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'account-exist');
        }
    }
</script>


<script>
    var loginName = document.getElementById("number");
    var mainPassword = document.getElementById("password");

    // Utility functions for button state management
    function disableButtonWithLoader(buttonId) {
        $('#' + buttonId).prop('disabled', true);
        $('.login_signup_loader').show();
    }

    function enableButtonWithoutLoader(buttonId) {
        $('#' + buttonId).prop('disabled', false);
        $('.login_signup_loader').hide();
    }

    // Wrapper function for resend OTP
    function resendOTP() {
        // Disable resend button temporarily
        $('.resendotp button').prop('disabled', true).css('opacity', '0.5');
        getOTPForSignup(true);
    }

    // Wrapper function for resend forgot password OTP
    function resendForgotPasswordOTP() {
        // Disable resend link temporarily
        $('.resendotp a').css('pointer-events', 'none').css('opacity', '0.5');
        getOTPForForgottenPassword(true);
    }

    //OPENING SIGNUP MODAL
    function signupModal() {
        if ('${session['wileySite']}' !='true'){
            forceRegisterMode=null;
            $('#signup').modal('show');
            document.getElementById("signUpTitle").innerText="Sign Up";
            document.getElementById("signUpSubTitle").innerText="";
            document.getElementById("migrateMobile").type="text";
            document.getElementById("migrateMobile").placeholder="Mobile no / Email ";
            $('#loginOpen').modal('hide');
            $('p.error-msg').text('');
            $('#loginPasswordError').text('');
            $('#nameError').text('');
            $('#migrateMobileError').text('');
            $('.verification-code').hide();
            $('#migrateOtp').show();
            $("#signupOtp").hide();
            operation = "signup";
        }else{
            $('.loading-icon').removeClass('hidden');
            window.location.href='/privatelabel/wileySignup?signUpPage=true';
        }
    }

    //Sign up Submit
    function formSubmit() {

        if ($('#name,#email,#mobile,#signup-password').val().length != 0) {
            // Disable register button and show loader
            disableButtonWithLoader('register');

            document.adduser.username.value = mobileNumber;
            if (mobileNumber.includes("@")) {
                document.adduser.email.value = mobileNumber;
            } else {
                document.adduser.mobile.value = mobileNumber;
            }

            var oData = new FormData(document.forms.namedItem("adduser"));
            oData.append("site", "Wonderslate");

            var url = "${createLink(controller:'creation',action:'addUser')}";

            var correctMobile = false;
            var correctEmail = false;
            var emailCheck = /^[A-Z0-9._%+-]+@([A-Z0-9-]+\.)+[A-Z]{2,4}$/i;
            if (/^\d{10}$/.test($("#mobile").val()) && $("#mobile").val() != "") {
                correctMobile = true;
                document.adduser.mobile.value = $("#mobile").val();
            }
            if ($("#email").val() != "" && $("#email").val().includes("@")) {
                if (emailCheck.test($("#email").val()))
                    correctEmail = true;
                document.adduser.email.value = $("#email").val();
            }
            if(forceRegisterMode=="quiz"){
                oData.append("forceRegisterMode", "quiz");
            }

            ajaxFunction(url, oData);

        }
    }

    function formSubmitForgot() {
        var fullname = document.getElementById("nameFromForgot");
        var password = document.getElementById("passwordFromForgot");
        if (fullname.value == '') {
            $('#nameErrorFromForgot').text('Please enter your name.');
            $(fullname).focus().addClass('input-error');
            $('#passwordFromForgotError').text('');
            $(password).removeClass('input-error');
        } else if (password.value == '') {
            $('#passwordFromForgotError').text('Please enter password.');
            $(password).focus().addClass('input-error');
        } else {
            if ($('#passwordFromForgot,#passwordFromForgot').val().length != 0) {
                // Disable CONTINUE button for forgot password and show loader
                $('input[onclick="javascript:formSubmitForgot();"]').prop('disabled', true);
                $('.login_signup_loader').show();

                document.adduserFromForgot.username.value = mobileNumber;
                if (mobileNumber.includes("@")) {
                    document.adduserFromForgot.email.value = mobileNumber;
                } else {
                    document.adduserFromForgot.mobile.value = mobileNumber;
                }

                var oData = new FormData(document.forms.namedItem("adduserFromForgot"));
                oData.append("site", "Wonderslate");

                var url = "${createLink(controller:'creation',action:'addUser')}";

                ajaxFunctionForgot(url, oData);
            }
        }
    }

    function ajaxFunction(url, oData) {
        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {
                loginDone(req);
            },
            error: function() {
                // Re-enable buttons on error
                enableButtonWithoutLoader('register');
                alert('Registration failed. Please try again.');
            }
        });
    }

    function ajaxFunctionForgot(url, oData) {
        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {
                loginDone(req);
            },
            error: function() {
                // Re-enable CONTINUE button for forgot password on error
                $('input[onclick="javascript:formSubmitForgot();"]').prop('disabled', false);
                $('.login_signup_loader').hide();
                alert('Registration failed. Please try again.');
            }
        });
    }

    //LOGIN CODES
    //OPEN Login modal
    function loginOpen() {
        $('#login-form').trigger('reset');
        $('p.error-msg').text('');
        $('#loginPasswordError').text('');
        $('#nameError').text('');
        $('#migrateMobileError').text('');

        if ($('.mega_menu__wrapper').hasClass('menu-showing')) {
            $(".navbar-hamburger").toggleClass("menu-actives");
            $(".mega_menu__overlay_bg").toggleClass("active");
            $(".mega_menu__wrapper").toggleClass("menu-showing");
        }
        $('#loginOpen').modal('show');
        $('#signup').modal('hide');
    }


    //FORGOT PASSWORD CODES

    function checkUserExist() {
        document.signin.username.value = document.signin.username_temp.value;
        var username = document.signin.username.value;
        var siteId = "${session["siteId"]}";
        <g:remoteFunction controller="creation" action="checkUserExistForSite"  onSuccess='userExistSuccess(data);'
         params="'siteId='+siteId+'&username='+username" />
    }

    //Get OTP
    function userExistSuccess(data) {
        if (data.status == 'No user') {
            $('#username-empty').text('There is no user with this username..');
        } else {
            $('#loginOpen').modal('hide');
            getOTPForSignup();
            $('#forgotPasswordmodal').modal('show');
        }
    }


    function loginOpenWithFunction(functionName, message) {
        functionNameToCall = functionName;
        if (loginHappened) {
            window[functionNameToCall]();
        } else {
            document.getElementById("loginMessage").innerText = message;
            document.getElementById("signupMessage").innerText = message;
            loginOpen();
        }
    }

    function signupWithFunction(functionName, message) {
        functionNameToCall = functionName;
        if (loginHappened) {
            window[functionNameToCall]();
        } else {
            $('#signup').modal('show');
            $('#loginOpen').modal('hide');
            $('p.error-msg').text('');
            $('#migrateMobileError').text('');
            $('#nameError').text('');
            $('#loginPasswordError').text('');

            document.getElementById("loginMessage").innerText = message;
            document.getElementById("signupMessage").innerText = message;
            operation = "signup";
            if("moveCartItems"==functionNameToCall){
                document.getElementById("name").value=document.getElementById("shipName").value;
                document.getElementById("migrateMobile").value=document.getElementById("shipMobile").value;
                document.getElementById("signup-password").value='';
                document.getElementById("signUpTitle").innerText = "User details confirmation";
            }
        }
    }

    $('#login-form input').keypress(function (e) {
        if (e.which == 13) {
            submitSignIn();
            return false;    //<---- Add this line
        }
    });

    $('#adduser input').keypress(function (e) {
        if (e.which == 13) {
            getOTPForSignup();
            return false;    //<---- Add this line
        }
    });
    $('#adduser input').keypress(function (e) {
        if (e.which == 13) {
            verifyMobileOTP();
            return false;    //<---- Add this line
        }
    });

    //Submit Login
    function submitSignIn() {
        document.signin.username.value = "${session["siteId"]}_" + document.signin.username_temp.value;
        var username = document.signin.username.value;
        var password = document.signin.password.value;
        if (loginName.value == '') {
            $('#usernameError').text('Please enter mobile number or email.');
            $(loginName).focus().addClass('input-error');
            $('#loginPasswordError').text('');
            $('#login-form #password').removeClass('input-error');
        } else if (password == '') {
            $('#loginPasswordError').text('Please enter password.');
            $('#login-form #password').addClass('input-error');
        } else {
            // Disable login button and show loader
            disableButtonWithLoader('nrmlSignin');
            <g:remoteFunction controller="log" action="login"  onSuccess="loginDone(data);" onFailure="loginFailed();" params="'source=web&username='+username+'&password='+password" />
        }
    }

    //Success Login/Signup
    var urlSeparate = location.search.split('?')[location.search.split('?').length - 1];
    var getGroup = urlSeparate.split('=')[0];
    if (getGroup == 'groupId') {
        $('.modal-dialog.modal-dialog-centered button.close').hide();
    }

    function loginDone(data) {
            // Re-enable all buttons and hide loader
            enableButtonWithoutLoader('nrmlSignin');
            enableButtonWithoutLoader('migrateOtp');
            enableButtonWithoutLoader('verifybtn');
            enableButtonWithoutLoader('register');
            $('input[onclick="javascript:verifyOTP();"]').prop('disabled', false);
            $('input[onclick="javascript:formSubmitForgot();"]').prop('disabled', false);

            if ('failed' == data.status) {
                alert(data.message);
            }
            if ("ok" == data.status || "OK" == data.status) {
                $('.loading-icon').removeClass('hidden');
                if(forceRegisterMode=="free"||forceRegisterMode=="paid"){
                    window.location.href = "/"+registerBookTitle+"/ebook?bookId="+registerBookId+"&siteName=${params.siteName}";
                }else  if(forceRegisterMode=="quiz"){

                    userId=data.username;
                    $('#loginOpen').modal('hide');
                    $('#signup').modal('hide');
                    $("#signupnav").hide();
                    if(document.getElementById("myhomeref")){
                        document.getElementById("myhomeref").href="/books/home";
                    }
                    window[returnFunctionCallForQuiz]();
                }else if(forceRegisterMode == "bookGPTLogin"){
                    window.location.href = window.location.href
                } else {
                    if (functionNameToCall != "") {
                        loginHappened = true;
                        $('#signup').modal('hide');
                        $('#loginOpen').modal('hide');
                        window[functionNameToCall]();
                    }
                    else if (siteIds != 1) {
                        <%if("true".equals(""+session["digitalLibraryLandingPage"])){%>
                        window.location.href = "/digitalLibrary";
                        <%}else{%>
                        window.location.href = "/wsLibrary/myLibrary";
                        <%}%>
                    } else if (getGroup == 'groupId') {
                        $('.modal-dialog.modal-dialog-centered button.close').hide();
                        window.location.reload();
                    } else {
                        loginHappened = true;
                        window.location.href = "/wsLibrary/myLibrary";
                    }
                }
            } else {
                $("#loginFailed").show();
            }
    }

    //HANDLE LOGIN FAILURE
    function loginFailed() {
        enableButtonWithoutLoader('nrmlSignin');
        alert('Login failed. Please try again.');
    }


    //FORGOT PASSWORD CODES
    //OPENING FORGOT PASSWORD MODAL
    function forgotPasswordOpen() {
        mainPassword.value='';
        if (loginName.value == "") {
            $(loginName).focus().addClass('input-error');
            $('#usernameError').text('Please enter registered mobile number or email to retrieve the password.');
            $('#login-form #password').removeClass('input-error');
            $('#loginPasswordError').text('');
        } else {
            operation = "forgotpassword";
            $('#forgot-form').trigger('reset');
            getOTPForForgottenPassword(false);
        }
    }

    //CHECKING USER IS EXISTS OR NOT
    function userExistSuccess(data) {
        if (operation == "forgotpassword") {
                $('#forgotPasswordmodal').modal('show');
                $('#loginOpen').modal('hide');
                $('#signup').modal('hide');
        }
    }

    //CALLING GENERATE OTP API
    function getOTPForSignup(resend=false) {
            counter = 60;
            userCheck = false;
            mobileNumber = document.getElementById("migrateMobile").value;
            if ($('#name').val() == '') {
                $('#nameError').text('Please enter name.');
                $('#name').focus().addClass('input-error');
            } else if (mobileNumber == '') {
                if(forceRegisterMode=="free"||forceRegisterMode=="paid"||forceRegisterMode=="quiz") {
                    $('#migrateMobileError').text('Please enter 10 digit mobile number.');
                }else{
                    $('#migrateMobileError').text('Please enter 10 digit mobile number or email.');
                }
                $('#migrateMobile').focus().addClass('input-error');
            } else if ($('#signup-password').val() == '') {
                $('#signupPasswordError').text('Please enter password.');
                $('#signup-password').focus().addClass('input-error');
            } else {
                if ($('#name,#email,#mobile,#signup-password').val().length != 0) {
                    if (/^\d{10}$/.test(mobileNumber) || mobileNumber.includes("@")) {
                        document.getElementById("sentMobileNumbers").innerHTML = mobileNumber;
                        // Disable GET OTP button and show loader
                        disableButtonWithLoader('migrateOtp');
                        if (mobileNumber.includes("@")) {
                            <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);' onFailure='OTPFailed();'
                        params="'email='+mobileNumber+'&source=web'" />
                            validation = "mobile";
                        } else {
                            <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);' onFailure='OTPFailed();'
                        params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck='+userCheck+'&source=web'" />
                            validation = "email";
                        }
                    } else {
                        $('#migrateMobileError').text('Please enter 10 digit mobile number or email.');
                        $('#migrateMobile').focus().addClass('input-error');
                        return false
                    }
                }
            }

    }

    //Timer
    var interval;
    var counter = 60;

    function resendTimer() {
        clearInterval(interval);
        interval = setInterval(function () {
            counter--;
            // Display 'counter' wherever you want to display it.
            if (counter <= 0) {
                clearInterval(interval);
                $('.timer').html("");
                $('.resendotp').show();
                return;
            } else {
                $('.timer').html("<br>Resend OTP option available in <b>" + counter + "</b> seconds");
            }
        }, 1000);
    }

    //AFTER SUCCESSFULLY RECEIVING OTP
    function OTPReceived(data) {
        var userExist = data.userExist;
        userExistSuccess(userExist);
        $('.resendotp').hide();
        // Re-enable GET OTP button and hide loader
        enableButtonWithoutLoader('migrateOtp');
        // Re-enable resend button
        $('.resendotp button').prop('disabled', false).css('opacity', '1');

        if (data.status == "Failed") {
            $('#user-exist').html('User already exists. Please use a different mobile number or email.');
        } else {
            resendTimer();
            if (operation == 'signup') {
                $('#signupOtp,.verification-code,#verifybtn').show();
                $('#signupOtp').focus();
                $('#migrateOtp').hide();
            }
        }
    }

    //HANDLE OTP GENERATION FAILURE
    function OTPFailed() {
        enableButtonWithoutLoader('migrateOtp');
        // Re-enable resend button
        $('.resendotp button').prop('disabled', false).css('opacity', '1');
        alert('Failed to generate OTP. Please try again.');
    }

    //VERIFYING OTP
    function verifyOTP() {
        if (document.getElementById("otpConfirm").value == "") {
            $('#otpConfirm').focus().addClass('input-error');
            $('#ForgotPasswordOTPError').text('Please enter the OTP to proceed.');
        } else {
            // Disable verify OTP button for forgot password and show loader
            $('input[onclick="javascript:verifyOTP();"]').prop('disabled', true);
            $('.login_signup_loader').show();

            var mobileOTP = document.getElementById("otpConfirm").value;
            if (mobileNumber.includes("@"))
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
    params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
            else
            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
    params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />

        }
    }

    //Submit otp for Signup

    function verifyMobileOTP() {
        if (document.getElementById("signupOtp").value == "") {
            $('#signupOtp').focus().addClass('input-error');
            $('#signupOTPError').text('Please enter the OTP to proceed.');
        } else {
            // Disable verify OTP button and show loader
            disableButtonWithLoader('verifybtn');

            var mobileOTP = document.getElementById("signupOtp").value;

            if (mobileNumber.includes("@"))
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
         params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
            else
            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
         params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />
        }

    }

    //OTP verified

    function otpVerified(data) {
        // Re-enable buttons and hide loader
        enableButtonWithoutLoader('verifybtn');
        $('input[onclick="javascript:verifyOTP();"]').prop('disabled', false);

        $('#signup .close').hide();
        if ("OK" == data.status) {
            if (operation == "signup") {
                if (data.userExists) {
                        if (data.allowLogin) {
                            if(forceRegisterMode=="quiz"){
                                userId=data.username;
                                $('#loginOpen').modal('hide');
                                $('#signup').modal('hide');
                                $("#signupnav").hide();
                                if(document.getElementById("myhomeref")){
                                    document.getElementById("myhomeref").href="/books/home";
                                }
                                window[returnFunctionCallForQuiz]();
                            }else {
                                if (functionNameToCall != "") {
                                    loginHappened = true;
                                    $('#signup').modal('hide');
                                    $('#signup').modal('hide');
                                    $('#loginOpen').modal('hide');
                                    window[functionNameToCall]();
                                }else {
                                    window.location.href = "/security/loginmanager";
                                    $('.loading-icon').removeClass('hidden');
                                }
                            }
                        } else {
                            document.getElementById('limit-signin-verify').innerText = 'The user cannot login as he already logged in from multiple devices';
                        }
                    } else {
                        $('.verification-code').html("<p class='register-success'>Your registration is successful.<br>Taking you inside the application now.</p>");

                        $('#migrateOtp,#signupOtp').hide();
                        $('#migrateMobile').attr('disabled', 'disabled');
                        $('.loading-icon').removeClass('hidden');
                        formSubmit();

                    }

            } else if (operation == "forgotpassword") {
                if (data.userExists) {
                    //temporary solution

                    if (data.allowLogin) {
                        if (functionNameToCall != "") {
                            loginHappened = true;
                            $('#signup').modal('hide');
                            $('#signup').modal('hide');
                            $('#loginOpen').modal('hide');
                            $('#forgotPasswordmodal').modal('hide');
                            window[functionNameToCall]();
                        }
                        else if(forceRegisterMode=="quiz"){
                            userId=data.username;
                            $('#loginOpen').modal('hide');
                            $('#signup').modal('hide');
                            $('#forgotPasswordmodal').modal('hide');
                            $("#signupnav").hide();
                            if(document.getElementById("myhomeref")){
                                document.getElementById("myhomeref").href="/books/home";
                            }
                            window[returnFunctionCallForQuiz]();
                        }else {
                            window.location.href = "/security/loginmanager?forgottenPassword=true";
                            $('.loading-icon').removeClass('hidden');
                        }
                    } else {
                        document.getElementById('limit-signin-verify').innerText = 'The user cannot login as he already logged in from multiple devices';
                    }
                } else {
                    $("#forgotTitle").text("Get started!");
                    $("#forgot-form, #forgotSubTitle").hide();
                    $("#signUpFromForgot").show();
                }
            } else if (operation == "migrate") {
                $('.set-password').show();
                $('.otp-wrapper').hide();
            }
        } else {
            if (operation == "signup") {
                $('#signupOtp').focus().addClass('input-error');
                $('#signupOTPError').text('Incorrect OTP. Please try again.');
            } else if (operation == "forgotpassword") {
                $('#otpConfirm').focus().addClass('input-error');
                $('#ForgotPasswordOTPError').text('Incorrect OTP. Please try again.');
            }
        }
    }

    //HANDLE OTP VERIFICATION FAILURE
    function otpVerificationFailed() {
        enableButtonWithoutLoader('verifybtn');
        $('input[onclick="javascript:verifyOTP();"]').prop('disabled', false);
        alert('OTP verification failed. Please try again.');
    }

    //ReSet Password
    function setPassword() {

        if (document.getElementById("setPassword1").value != document.getElementById("setPassword2").value) {
            $('#passwd-error').html('Please make sure password and confirm password are same.');
        } else if (document.getElementById("setPassword1").value == "") {
            $('#passwd-error').html('Please enter the password.')

        } else {
            $('.login_signup_loader').show();
            var oldPassword = "<%= session['userdetails']!=null?session['userdetails'].password:"" %>";
            var password = document.getElementById("setPassword1").value;
            <g:remoteFunction controller="creation" action="updateMigrateUserPassword"  onSuccess='passwordSetCompleted(data);'
    params="'oldPassword='+oldPassword+'&password='+password" />
        }
    }

    //Password set success
    function passwordSetCompleted(data) {
        $('.login_signup_loader').hide();
        window.location.href = "/security/loginmanager";
    }

    function checkLoginAndProceed(destination) {
        <sec:ifLoggedIn>
        $(".loading-icon").removeClass("hidden");
        window.location.href = destination;
        </sec:ifLoggedIn>
        <sec:ifNotLoggedIn>
        nextDestination = destination;
        </sec:ifNotLoggedIn>
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    function getOTPForForgottenPassword(resend) {
        counter = 60;
        mobileNumber = document.getElementById("number").value;

        if (/^\d{10}$/.test(mobileNumber) || mobileNumber.includes("@")) {
            document.getElementById("sentMobileNumber").innerHTML = mobileNumber;
            $('.login_signup_loader').show();
            if (mobileNumber.includes("@")) {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='forgotPasswordOTPReceived(data);' onFailure='forgotPasswordOTPFailed();'
            params="'email='+mobileNumber+'&source=web'" />
                validation = "mobile";
            } else {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='forgotPasswordOTPReceived(data);' onFailure='forgotPasswordOTPFailed();'
            params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck=true&source=web'" />
                validation = "email";
            }

        } else {
            $(loginName).focus().addClass('input-error');
            $('#usernameError').text('Please enter 10 digit mobile number or email.');
            $('#login-form #password').removeClass('input-error');
            $('#loginPasswordError').text('');
            return false
        }

    }

    function forgotPasswordOTPReceived(data) {
        var userExist = data.userExist;
        userExistSuccess(userExist);
        $('#otpConfirm').focus();
        $('.resendotp').hide();
        $('.login_signup_loader').hide();
        // Re-enable resend link
        $('.resendotp a').css('pointer-events', 'auto').css('opacity', '1');
        resendTimer();
    }

    //HANDLE FORGOT PASSWORD OTP GENERATION FAILURE
    function forgotPasswordOTPFailed() {
        $('.login_signup_loader').hide();
        // Re-enable resend link
        $('.resendotp a').css('pointer-events', 'auto').css('opacity', '1');
        alert('Failed to generate OTP. Please try again.');
    }

    //setting the default focus
    $('#loginOpen').on('shown.bs.modal', function () {
        $('#number').focus();
    })

    $('#forgotPasswordmodal').on('shown.bs.modal', function () {
        document.getElementById("signInId").reset();
        $('#otpConfirm').focus();
    })

    $('#signup').on('shown.bs.modal', function () {
        $('#name').focus();
        $('#signupOTPError').text('');
    })

    $(loginName).on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#usernameError').text('');
        $('#loginFailed').hide();
    });
    $('#login-form #password').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#loginPasswordError').text('');
        $('#loginFailed').hide();
    });
    $('#name').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#nameError').text('');
    });
    $('#migrateMobile').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#migrateMobileError').text('');
    });
    $('#signup-password').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#signupPasswordError').text('');
    });
    $('#signupOtp').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#signupOTPError').text('');
    });
    $('#otpConfirm').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#ForgotPasswordOTPError').text('');
    });
    $('#nameFromForgot').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#nameErrorFromForgot').text('');
    });
    $('#passwordFromForgot').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#passwordFromForgotError').text('');
    });

    //hide or show password
    $('.hide-password').on('click', function(e){
        e.preventDefault();
        var $this= $(this), $password_field = $this.prev('input');
        ( 'password' == $password_field.attr('type') ) ? $password_field.attr('type', 'text') : $password_field.attr('type', 'password');
        ( 'visibility' == $this.text() ) ? $this.text('visibility_off') : $this.text('visibility');
    });

    function openRegister(bookType,inputBookTitle,inputBookId){
        registerBookTitle = replaceAll(inputBookTitle,' ','-');
        registerBookId = inputBookId;

        if(bookType=="free"){
            signupModal();
            forceRegisterMode = "free";
            document.getElementById("signUpTitle").innerText="Get this eBook for free!";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpSubTitle").innerText="Enter the following information to get this eBook.";

        }
        else if(bookType=="paid"){
            signupModal();
            forceRegisterMode = "paid";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpTitle").innerText="Get a chapter for free!";
            document.getElementById("signUpSubTitle").innerText="Enter the following information to get a free chapter.";

        }
    }

    function openRegisterForQuiz(returnFunctionCall){
        returnFunctionCallForQuiz = returnFunctionCall;
        $('.loading-icon').addClass('hidden');
            signupModal();
            forceRegisterMode = "quiz";
            document.getElementById("signUpTitle").innerText="Well done. You are almost there!";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpSubTitle").innerText="Enter the following information and check your quiz results.";



    }

    function resetError(){
        $("#loginFailed").hide();
    }

    $('#loginOpen').on('hidden.bs.modal', function () {
        $('#number, #login-form #password').removeClass('input-error');
        $('#usernameError, #loginPasswordError').text('');
        $('#loginFailed').hide();
    });

    $('#forgotPasswordmodal').on('hidden.bs.modal', function () {
        $('#otpConfirm, #nameFromForgot, #passwordFromForgot').removeClass('input-error');
        $('#ForgotPasswordOTPError, #nameErrorFromForgot, #passwordFromForgotError').text('');
        $("#forgotTitle").text("Forgot Password");
        $("#forgotSubTitle, #forgot-form").show();
        $("#signUpFromForgot").hide();
    });

    $('#signup').on('hidden.bs.modal', function () {
        $('#name, #migrateMobile, #signup-password, #signupOtp').removeClass('input-error');
        $('#nameError, #migrateMobileError, #signupPasswordError, #signupOTPError').text('');
    });

    function openAccessCodePage(){
        window.location.href ="/wsLibrary/accessCode";
    }

    if("${params.mode}"=="loginform"){
        document.addEventListener('DOMContentLoaded',loginOpen)
    }
</script>

