<div class="modal fade book-cart-modal modal-modifier" id="AddToCartModal">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-left">

                <h5 id="cartModalBookTitle" class="mt-2 mb-4 mb-md-3 px-md-3 px-0"></h5>

                <div class="book_variants d-flex col-12 p-md-3 px-0">

                    <div class="col pr-2 pr-lg-3 pl-0">
                        <div class="card card-modifier border-0 shadow-sm mb-3 ebook-variant p-3">
                            <div class="card-body p-0">
                                <h6><strong>eBook</strong></h6>
                                <span id="ebook_ListPrice" class="list_price">Loading...</span> <span id="ebook_OfferPrice" class="offer_price"></span>
                                <span class="d-block d-lg-inline book_validity" style="font-size: 13px;color: #EE3539;"></span>
                                <h6 id="ebook_OfferPercentage" class="text-success mb-0 offer_percentage"></h6>
                            </div>
                            <button style="background: transparent;border: none" id="ebook_AddToCartBtn" class="btn btn-outline-primary btn-outline-primary-modifier btn-shadow mb-2 w-100">Add to Cart</button>
                        </div>
                    </div>

                    <div class="col pl-2 pl-lg-3 pr-0">
                        <div class="card card-modifier shadow-sm mb-3 test-series-variant p-3">
                            <div class="card-body p-0">
                                <span class="badge badge-info">Popular</span>
                                <h6><strong>Online Test Series + eBook</strong></h6>
                                <span id="TS_ListPrice" class="list_price">Loading...</span><span id="TS_OfferPrice" class="offer_price"></span>
                                <span class="d-block d-lg-inline book_validity" style="font-size: 13px;color: #EE3539;"></span>
                                <h6 id="TS_OfferPercentage" class="text-success mb-0 offer_percentage"></h6>
                            </div>
                            <button style="background: transparent;border: none" id="TS_AddToCartBtn" class="btn btn-primary btn-primary-modifier btn-shadow mb-2 w-100">Add to Cart</button>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade book-cart-modal modal-modifier" id="bookCartModal">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation" style="display: none;">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div id="existIconAnimation" class="f-modal-alert" style="display: none;">
                    <div class="f-modal-icon f-modal-warning scaleAnimation">
                        <span class="f-modal-body pulseAnimationIns"></span>
                        <span class="f-modal-dot pulseAnimationIns"></span>
                    </div>
                </div>
                <h5 id="cartModalText" class="mt-3">Book is added to your cart!</h5>

                <div id="cartModalBtns" class="d-none justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">Continue Browsing</button>
                    <button type="button" onclick="goCartPage();" class="btn btn-lg btn-primary btn-shadow border-0 col-6 col-md-5 ml-2" data-dismiss="modal" aria-label="Close">Go to Cart</button>
                </div>
                <div id="cartModalLibBtn" class="d-none justify-content-center py-3 col-12">
                    <button type="button" onclick="goLibraryPage();" class="btn btn-lg btn-primary btn-shadow border-0 col-6 col-md-5 ml-2" data-dismiss="modal" aria-label="Close">Go to Library</button>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    // Shopping cart functions
    var cartItemsCount;
    var prepjoySite = "${session['prepjoySite']}";
    function showAddToCartModal(bookId,bookTitle,ebook_ListPrice,ebook_OfferPrice,TS_ListPrice,TS_OfferPrice) {

        $("#AddToCartModal").modal('show');
        $("#cartModalBookTitle").html(bookTitle);

        // eBooks
        if (ebook_ListPrice !=0 && ebook_ListPrice !=null ){
            var ebook_Percentage = (ebook_ListPrice - ebook_OfferPrice);
            ebook_Percentage = ebook_ListPrice>100 ? "Rs."+(ebook_ListPrice - ebook_OfferPrice) : ((ebook_Percentage * 100 / ebook_ListPrice).toFixed(0)) +"%";
        }

        if (ebook_ListPrice == ebook_OfferPrice) {
            $('#ebook_ListPrice').html("");
            $('#ebook_OfferPercentage').html("");
        } else {
            $('#ebook_ListPrice').html("<del><span class='rupee-symbol'>&#x20b9</span>"+ebook_ListPrice+"</del>");
            $('#ebook_OfferPercentage').html(ebook_Percentage + " Off");
        }

        $('#ebook_OfferPrice').html("<span class='rupee-symbol'>&#x20b9</span>"+ebook_OfferPrice);
        $("#ebook_AddToCartBtn").attr('href', 'javascript:addToCart('+bookId+',\'eBook\')');

        // Test Series
        if (TS_ListPrice !=0 && TS_ListPrice !=null ){
            var TS_Percentage = (TS_ListPrice - TS_OfferPrice);
            TS_Percentage = TS_ListPrice>100 ? "Rs."+(TS_ListPrice - TS_OfferPrice) : ((TS_Percentage * 100 / TS_ListPrice).toFixed(0)) +"%";
        }

        console.log(TS_Percentage);

        if (TS_ListPrice == TS_OfferPrice) {
            $('#TS_ListPrice').html("");
            $('#TS_OfferPercentage').html("");
        } else {
            $('#TS_ListPrice').html("<del><span class='rupee-symbol'>&#x20b9</span>"+TS_ListPrice+"</del>");
            $('#TS_OfferPercentage').html(TS_Percentage + " Off");
        }

        $('#TS_OfferPrice').html("<span class='rupee-symbol'>&#x20b9</span>"+TS_OfferPrice);
        $("#TS_AddToCartBtn").attr('href', 'javascript:addToCart('+bookId+',\'testSeries\')');


    }

    function addToCart(bookId,bookType) {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId+'&bookType='+bookType" onSuccess='booksAddedInCart(bookId, data);'/>
        if(bookType == "eBook") {
            $("#ebook_AddToCartBtn").html("Adding..");
            $("#addToCartBtn"+bookId).html("Adding..");
        } else {
            $("#TS_AddToCartBtn").html("Adding..");
        }
    }

    function booksAddedInCart(id, data) {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if (data.status == "OK") {
            $("#AddToCartModal").modal('hide');
            $("#bookCartModal").modal('show');
            $("#cartModalText").html("This Book has been added to your cart.");
            <%if("100".equals(""+session["siteId"])){%>
            $("#cartModalText").html("This AI Tutor has been added to your cart.");
            <%}%>
            $("#addedIconAnimation").show();
            $("#existIconAnimation").hide();
            var ct = Number($('#navbarCartCount').text()) + 1;
            $('#navbarCartCount').text(ct);
            $("#cartModalBtns").removeClass('d-none').addClass('d-flex');
            $("#cartModalLibBtn").removeClass('d-flex').addClass('d-none');
            $("#ebook_AddToCartBtn").html("Add to Cart");
            $("#TS_AddToCartBtn").html("Add to Cart");
            $("#addToCartBtn"+id).html("Add to Cart");
        } else if (data.status == "Already exist") {
            $("#AddToCartModal").modal('hide');
            $("#bookCartModal").modal('show');
            $("#cartModalText").html("This Book is already present in your cart.");
            <%if("100".equals(""+session["siteId"])){%>
            $("#cartModalText").html("This AI Tutor is already present in your cart.");
            <%}%>
            $("#existIconAnimation").show();
            $("#addedIconAnimation").hide();
            $("#cartModalBtns").removeClass('d-none').addClass('d-flex');
            $("#cartModalLibBtn").removeClass('d-flex').addClass('d-none');
            $("#ebook_AddToCartBtn").html("Add to Cart");
            $("#TS_AddToCartBtn").html("Add to Cart");
            $("#addToCartBtn"+id).html("Add to Cart");
        } else {
            $("#AddToCartModal").modal('hide');
            $("#bookCartModal").modal('show');
            $("#cartModalText").html("This Book is already purchased.");
            <%if("100".equals(""+session["siteId"])){%>
            $("#cartModalText").html("This AI Tutor is already purchased.");
            <%}%>
            $("#existIconAnimation").show();
            $("#addedIconAnimation").hide();
            $("#cartModalBtns").removeClass('d-flex').addClass('d-none');
            $("#cartModalLibBtn").removeClass('d-none').addClass('d-flex');
            $("#ebook_AddToCartBtn").html("Add to Cart");
            $("#TS_AddToCartBtn").html("Add to Cart");
            $("#addToCartBtn"+id).html("Add to Cart");
        }
    }

    function goCartPage() {
        window.open('/wsshop/cart', '_blank');
    }

    function goLibraryPage() {
        window.open('/libraryBooks/myLibrary?mode=mybooks', '_blank');
    }

    // For book detail and preview pages
    function addToCartFromDtl(bookId,bookType) {

           if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId+'&bookType='+bookType" onSuccess='redirectToCartPage(bookId, data);'/>
            $("#buyNow").html("Adding..");

    }
    function addSubscriptionToCartFromDtl(bookId,subscriptionId,subsStartingBookId,subsDuration) {
        if(userLoggedIn){
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId+'&bookType=subscription&subscriptionId='+subscriptionId+'&subsStartingBookId='+subsStartingBookId+'&subsDuration='+subsDuration" onSuccess='redirectToCartPage(bookId, data);'/>
            $("#buyNow").html("Adding..");
        } else {
            if (!prepjoySite){
                loginOpen();
            }else{
                $('#prepjoySignupModal').modal('show');
            }
        }
    }
    function redirectToCartPage(id, data) {
        $("#buyNow").html("Add to Cart");
        if (data.status == "OK") {
            window.location.href = "/wsshop/cart";
            var ct = Number($('#navbarCartCount').text()) + 1;
            $('#navbarCartCount').text(ct);
        } if (data.status == "Already exist") {
            window.location.href = "/wsshop/cart";
        }
    }

    window.addEventListener( "pageshow", function () {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
    }, false);


        $("#navbarCartCount").html("${session["userCartCount"]}");

</script>
