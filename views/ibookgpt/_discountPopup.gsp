<%-- Discount Code Popup Modal --%>
<div id="discountPopupModal" class="discount-modal" style="display: none;">
    <div class="discount-modal-overlay"></div>
    <div class="discount-modal-content">
        <button class="discount-close-btn" onclick="closeDiscountPopup()">&times;</button>
        
        <div class="discount-header">
            <div class="discount-icon">
                <i class="fas fa-gift"></i>
            </div>
            <h2 class="discount-title">Special Discount Available!</h2>
            <div id="discountNameSection" class="discount-name-section" style="display: none;">
                <p class="discount-name-text">For <span id="discountNameText"></span> users</p>
            </div>
            <p class="discount-subtitle">Use this exclusive code to get your discount</p>
        </div>
        
        <div class="discount-code-section">
            <div class="discount-code-container">
                <span class="discount-code-label">Discount Code:</span>
                <div class="discount-code-display">
                    <span id="discountCodeText" class="discount-code-text"></span>
                    <button class="copy-code-btn" onclick="copyDiscountCode()" title="Copy to clipboard">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="discount-actions">
            <button class="shop-now-btn" onclick="proceedToShop()">
                SHOP NOW
            </button>
        </div>
    </div>
</div>

<style>
.discount-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.discount-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.discount-modal-content {
    position: relative;
    background: linear-gradient(135deg, #2dd4bf 0%, #06b6d4 100%);
    border-radius: 20px;
    padding: 40px 30px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.4s ease-out;
    color: white;
}

.discount-close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 24px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.discount-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.discount-header {
    margin-bottom: 30px;
}

.discount-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5rem;
    animation: pulse 2s infinite;
}

.discount-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: white;
}

.discount-name-section {
    margin: 10px 0;
}

.discount-name-text {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: #FFE066;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.discount-subtitle {
    font-size: 1rem;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

.discount-code-section {
    margin-bottom: 30px;
}

.discount-code-container {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.discount-code-label {
    display: block;
    font-size: 0.9rem;
    margin-bottom: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.discount-code-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.discount-code-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    letter-spacing: 2px;
    font-family: 'Courier New', monospace;
}

.copy-code-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.copy-code-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.shop-now-btn {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.shop-now-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Mobile responsive */
@media (max-width: 480px) {
    .discount-modal-content {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .discount-title {
        font-size: 1.5rem;
    }
    
    .discount-code-text {
        font-size: 1.2rem;
    }
}
</style>

<script>
function showDiscountPopup(discountCode, discountName) {
    if (!discountCode) return;

    document.getElementById("discountCodeText").textContent = discountCode;

    // Show discount name section if discountName is provided
    var discountNameSection = document.getElementById("discountNameSection");
    var discountNameText = document.getElementById("discountNameText");

    if (discountName && discountName.trim() !== "") {
        discountNameText.textContent = discountName;
        discountNameSection.style.display = "block";
    } else {
        discountNameSection.style.display = "none";
    }

    document.getElementById("discountPopupModal").style.display = "flex";

    // Note: Tracking is handled by the controller, no need to track here
}

function closeDiscountPopup() {
    document.getElementById("discountPopupModal").style.display = "none";
}

function copyDiscountCode() {
    var codeText = document.getElementById("discountCodeText").textContent;
    
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(codeText).then(function() {
            showCopySuccess();
        }).catch(function() {
            fallbackCopyTextToClipboard(codeText);
        });
    } else {
        fallbackCopyTextToClipboard(codeText);
    }
}

function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        var successful = document.execCommand("copy");
        if (successful) {
            showCopySuccess();
        }
    } catch (err) {
        console.error("Fallback: Oops, unable to copy", err);
    }
    
    document.body.removeChild(textArea);
}

function showCopySuccess() {
    var copyBtn = document.querySelector(".copy-code-btn");
    var originalHTML = copyBtn.innerHTML;
    copyBtn.innerHTML = '<i class="fas fa-check"></i>';
    copyBtn.style.background = "rgba(34, 197, 94, 0.8)";
    
    setTimeout(function() {
        copyBtn.innerHTML = originalHTML;
        copyBtn.style.background = "rgba(255, 255, 255, 0.2)";
    }, 2000);
}

function proceedToShop() {
    closeDiscountPopup();
    // You can add navigation logic here if needed
}

// Note: Tracking is now handled by the controller when the page loads
// No need for separate AJAX tracking calls

// Close modal when clicking outside
document.addEventListener("click", function(event) {
    var modal = document.getElementById("discountPopupModal");
    if (event.target === modal) {
        closeDiscountPopup();
    }
});

// Close modal with Escape key
document.addEventListener("keydown", function(event) {
    if (event.key === "Escape") {
        closeDiscountPopup();
    }
});
</script>
