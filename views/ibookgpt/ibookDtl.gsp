<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "ibookgpt");
newCookie.path = "/"
response.addCookie newCookie;
%>
<style>
/* Critical styles to prevent flash of unstyled content */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

.page-main-wrapper {
    background: #f8f4f0 !important;
}

    .book-details-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem 1rem 0 1rem;
    }

    .book-details-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .book-header {
        padding: 2rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .book-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: #2d3748;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .book-pricing {
        margin-bottom: 1.5rem;
    }

    .price-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }



    .price-period {
        font-size: 1.2rem;
        color: #64748b;
        font-weight: 500;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    /* New styles for book header layout */
    .book-header-content {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .book-cover-section {
        flex-shrink: 0;
    }

    .main-book-cover {
        width: 200px;
        height: 280px;
        object-fit: cover;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .book-info-section {
        flex: 1;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .book-header-content {
            flex-direction: column;
            gap: 1.5rem;
            align-items: center;
            text-align: center;
        }

        .book-cover-section {
            align-self: center;
        }

        .main-book-cover {
            width: 160px;
            height: 224px;
        }

        .book-title {
            font-size: 1.8rem;
        }
    }

    /* Updated pricing styles */
    .current-price {
        font-size: 2rem;
        font-weight: 700;
        color: #059669;
    }

    .original-price {
        font-size: 1.2rem;
        color: #64748b;
        text-decoration: line-through;
    }

    .discount-badge {
        background: #ef4444;
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    /* Dummy cover styles for main book */
    .dummy-cover-main {
        width: 200px;
        height: 280px;
        display: flex;
        flex-direction: column;
        border: 1px solid #ccc;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        border-radius: 12px;
        background: white;
    }

    .dummy-cover-section-top {
        display: flex !important;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        padding: 8px 12px;
        background-color: white;
        font-weight: bold;
        font-size: 10px;
        color: black;
        border-bottom: 1px solid #eee;
    }

    .dummy-cover-section-middle {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 15px;
        background: #667eea;
        color: white;
    }

    .dummy-cover-title {
        font-size: 14px;
        font-weight: bold;
        line-height: 1.2;
        margin: 0;
    }

    .dummy-cover-section-bottom {
        padding: 8px 12px;
        text-align: center;
        background-color: #f8f9fa;
        font-size: 10px;
        color: #666;
        border-top: 1px solid #eee;
    }

    .dummy-cover-brand {
        font-weight: bold;
        color: #667eea;
    }

    .dummy-cover-brand .gpt {
        color: #764ba2;
    }

    /* Subject book cover styles */
    .subject-book-cover {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
    }

    /* Dummy cover styles for subject books */
    .dummy-cover-small {
        width: 80px;
        height: 100px;
        display: flex;
        flex-direction: column;
        border: 1px solid #ccc;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border-radius: 8px;
        background: white;
        margin: 0 auto 1rem;
    }

    .dummy-cover-section-top-small {
        display: flex !important;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        padding: 3px 5px;
        background-color: white;
        font-weight: bold;
        font-size: 6px;
        color: black;
        border-bottom: 1px solid #eee;
    }

    .dummy-cover-section-middle-small {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 5px;
        color: white;
    }

    .dummy-cover-title-small {
        font-size: 8px;
        font-weight: bold;
        line-height: 1.1;
        margin: 0;
    }

    .dummy-cover-section-bottom-small {
        padding: 3px 5px;
        text-align: center;
        background-color: #f8f9fa;
        font-size: 6px;
        color: #666;
        border-top: 1px solid #eee;
    }

    .dummy-cover-brand-small {
        font-weight: bold;
        color: #667eea;
        font-size: 5px;
    }

    .dummy-cover-brand-small .gpt {
        color: #764ba2;
    }

    .btn-add-cart {
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
    }

    .btn-add-cart:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
    }

    .btn-preview {
        background: transparent;
        color: #64748b;
        border: 2px solid #e2e8f0;
        padding: 10px 20px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-preview:hover {
        border-color: #0ea5e9;
        color: #0ea5e9;
    }

    .included-subjects {
        padding: 2rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 1.5rem;
    }

    .subjects-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-bottom: 2rem;
        justify-content: flex-start;
    }

    .subject-card {
        background: #f8fafc;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 2px solid transparent;
        width: 180px;
        flex-shrink: 0;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .subject-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
    }

    .subject-cover {
        width: 80px;
        height: 100px;
        border-radius: 8px;
        margin: 0 auto 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Default subject cover for fallback */
    .subject-cover-default {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .subject-name {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
    }

    .features-section {
        padding: 2rem;
        background: #f8fafc;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .feature-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .feature-icon-blue {
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
    }

    .feature-icon-green {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .feature-icon-purple {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }

    .feature-icon-orange {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .feature-icon-red {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .feature-icon-teal {
        background: linear-gradient(135deg, #14b8a6, #0d9488);
    }

    .feature-text {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
    }

    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f4f6;
        border-top: 4px solid #0ea5e9;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .book-title {
            font-size: 2rem;
        }

        .price-amount {
            font-size: 1.5rem;
        }

        .book-pricing {
            flex-direction: column;
            align-items: flex-start;
        }

        .action-buttons {
            width: 100%;
            justify-content: stretch;
        }

        .btn-add-cart, .btn-preview {
            flex: 1;
        }

        .subjects-grid {
            gap: 1rem;
        }

        .subject-card {
            width: 140px;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

    <div class="book-details-container">
        <g:if test="${bookDetails}">
            <!-- Book Details Card -->
            <div class="book-details-card">
                <!-- Book Header -->
                <div class="book-header">
                    <div class="book-header-content">
                        <div class="book-cover-section">
                            <g:if test="${bookDetails.coverImage}">
                                <g:if test="${bookDetails.coverImage.startsWith('https')}">
                                    <img src="${bookDetails.coverImage}" alt="${bookDetails.title}" class="main-book-cover">
                                </g:if>
                                <g:else>
                                    <img src="/funlearn/showProfileImage?id=${bookDetails.id}&fileName=${bookDetails.coverImage}&type=books&imgType=passport" alt="${bookDetails.title}" class="main-book-cover">
                                </g:else>
                            </g:if>
                            <g:else>
                                <div class="dummy-cover-main">
                                    <div class="dummy-cover-section-top">
                                        <span>Class Book</span>
                                        <span class="dummy-cover-brand">iBook<span class="gpt">GPT</span></span>
                                    </div>
                                    <div class="dummy-cover-section-middle" style="background: ${bookDetails.coverColor ?: '#667eea'};">
                                        <p class="dummy-cover-title">${bookDetails.title?.take(40) ?: 'AI Tutor Book'}</p>
                                    </div>
                                    <div class="dummy-cover-section-bottom">
                                        All Subjects
                                    </div>
                                </div>
                            </g:else>
                        </div>
                        <div class="book-info-section">
                            <h1 class="book-title">${bookDetails.title ?: 'AI Tutor Book'}</h1>
                            <div class="book-pricing">
                                <div class="price-section">
                                    <span class="current-price">₹<g:formatNumber number="${bookDetails.price ?: 0}" type="number" groupingUsed="true"/></span>
                                    <span class="original-price">₹<g:formatNumber number="${(bookDetails.listPrice ?: 0)}" type="number" groupingUsed="true"/></span>
                                    <%
                                        def offerPrice = bookDetails.price ?: 0
                                        def actualPrice = bookDetails.listPrice ?: offerPrice
                                        def discountPercentage = 0
                                        if (actualPrice > 0 && offerPrice < actualPrice) {
                                            def calculatedVal = actualPrice - offerPrice
                                            discountPercentage = Math.round((calculatedVal * 100) / actualPrice)
                                        }
                                    %>
                                    <g:if test="${discountPercentage > 0}">
                                        <span class="discount-badge">${discountPercentage}% OFF</span>
                                    </g:if>
                                    <span class="price-period">/ year</span>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn-add-cart" onclick="addToCartI(${bookDetails.id})">
                                    <i class="fas fa-shopping-cart"></i> Add to Cart
                                </button>
                                <button class="btn-preview" onclick="previewBook(${bookDetails.id})">
                                    Check AI Tutor
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Included Subjects -->
                <div class="included-subjects">
                    <h2 class="section-title">Included Subjects</h2>

                    <div class="subjects-grid">
                        <g:if test="${bookDetails.packageBooks && bookDetails.packageBooks.size() > 0}">
                            <g:each in="${bookDetails.packageBooks}" var="packageBook">
                                <div class="subject-card">
                                    <g:if test="${packageBook.coverImage}">
                                        <g:if test="${packageBook.coverImage.startsWith('https')}">
                                            <img src="${packageBook.coverImage}" alt="${packageBook.subject}" class="subject-book-cover" style="width: 80px; height: 100px; object-fit: cover; border-radius: 8px; margin: 0 auto 1rem; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                                        </g:if>
                                        <g:else>
                                            <img src="/funlearn/showProfileImage?id=${packageBook.id}&fileName=${packageBook.coverImage}&type=books&imgType=passport" alt="${packageBook.subject}" class="subject-book-cover" style="width: 80px; height: 100px; object-fit: cover; border-radius: 8px; margin: 0 auto 1rem; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                                        </g:else>
                                    </g:if>
                                    <g:else>
                                        <div class="dummy-cover-small">
                                            <div class="dummy-cover-section-top-small">
                                                <span></span>
                                                <span class="dummy-cover-brand-small">iBook<span class="gpt">GPT</span></span>
                                            </div>
                                            <div class="dummy-cover-section-middle-small" style="background: ${packageBook.coverColor ?: '#667eea'};">
                                                <p class="dummy-cover-title-small">${packageBook.title}</p>
                                            </div>
                                            <div class="dummy-cover-section-bottom-small">
                                                ${packageBook.subject}
                                            </div>
                                        </div>
                                    </g:else>
                                    <div class="subject-name">${packageBook.subject}</div>
                                </div>
                            </g:each>
                        </g:if>

                    </div>
                </div>

                <!-- iBookGPT Features -->
                <div class="features-section">
                    <h2 class="section-title">iBookGPT Features</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon feature-icon-blue">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="feature-text">Instant Doubt Solving</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon feature-icon-green">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="feature-text">Chapter Summaries & Exam Notes</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon feature-icon-purple">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="feature-text">Comprehensive Question Bank</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon feature-icon-orange">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="feature-text">Previous Year Questions (PYQs)</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon feature-icon-red">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <div class="feature-text">Mock Tests & Auto-Evaluation</div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon feature-icon-teal">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-text">Performance Analytics</div>
                        </div>
                    </div>
                </div>
            </div>
        </g:if>
        <g:else>
            <!-- Error State -->
            <div class="book-details-card">
                <div class="book-header">
                    <div style="text-align: center; padding: 2rem;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ef4444; margin-bottom: 1rem;"></i>
                        <h2 style="color: #ef4444; margin-bottom: 1rem;">Book Not Found</h2>
                        <p style="color: #64748b;">The requested book could not be found.</p>
                        <a href="/ibookgpt/istore" class="btn-preview" style="margin-top: 1rem; text-decoration: none;">
                            <i class="fas fa-arrow-left"></i> Back to Store
                        </a>
                    </div>
                </div>
            </div>
        </g:else>
    </div>

    <script>
    function addToCartI(bookId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId+'&bookType=bookGPT'" onSuccess='booksAddedInCart(bookId, data);'/>
    }

    function booksAddedInCart(id, data) {
        window.location.href = "/wsshop/cart";
    }
    function previewBook(bookId) {
        window.open("/prompt/bookgpt?bookId=" + bookId, "_blank");
    }
    </script>

<%-- Include discount popup modal --%>
<g:render template="/ibookgpt/discountPopup"></g:render>

<script>
document.addEventListener("DOMContentLoaded", function() {
    <g:if test="${showDiscountPopup}">
        // Show discount popup after a short delay
        setTimeout(function() {
            showDiscountPopup("${discountCode}", "${discountName ?: ''}");
        }, 1200);
    </g:if>
});
</script>

<g:render template="/privatelabel/footer_new"></g:render>

