<g:render template="/privatelabel/navheader_new"></g:render>

<!-- Hero Section -->
<section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 120px 0 80px; min-height: 600px; position: relative; overflow: hidden;">
    <!-- Background Pattern -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%); opacity: 0.3;"></div>

    <div class="container" style="position: relative; z-index: 2;">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title mb-4" style="font-size: 3.5rem; font-weight: 700; color: #ffffff; line-height: 1.2; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    The Future of Learning, Powered by <span style="color: #ffd700;">iBook</span><span style="color: #ff6b6b;">GPT</span>
                </h1>
                <p class="hero-subtitle mb-4" style="font-size: 1.25rem; color: rgba(255,255,255,0.9); line-height: 1.6;">
                    Your AI Tutor for every class. Trusted by students, institutions, and publishers.
                </p>
                <a href="/istore?siteName=${session["siteName"]}&" class="btn btn-primary btn-lg" style="background: linear-gradient(45deg, #ff6b6b, #ffd700); border: none; padding: 15px 30px; font-size: 1.1rem; border-radius: 25px; box-shadow: 0 8px 25px rgba(0,0,0,0.2); color: #333; font-weight: 600; transition: all 0.3s ease;"
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 35px rgba(0,0,0,0.3)'"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.2)'">
                    Explore AI Tutor
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-illustration" style="position: relative;">
                    <!-- AI Tutor Illustration -->
                    <div style="background: white; border-radius: 20px; padding: 40px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto;">
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                            <div style="width: 80px; height: 80px; background: #4285f4; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px;">
                                <i class="fas fa-robot" style="color: white; font-size: 2rem;"></i>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px 20px; border-radius: 15px; border: 2px solid #e9ecef;">
                                <div style="font-size: 1.1rem; color: #333;">👋 Hi! I'm your AI Tutor</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-around; margin-top: 30px;">
                            <div style="text-align: center;">
                                <div style="width: 50px; height: 50px; background: #34a853; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                    <i class="fas fa-book" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <small style="color: #666;">Study</small>
                            </div>
                            <div style="text-align: center;">
                                <div style="width: 50px; height: 50px; background: #fbbc04; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                    <i class="fas fa-question-circle" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <small style="color: #666;">Ask</small>
                            </div>
                            <div style="text-align: center;">
                                <div style="width: 50px; height: 50px; background: #ea4335; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                    <i class="fas fa-chart-line" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <small style="color: #666;">Learn</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- AI Tutor for Every Class Section -->
<section class="ai-tutor-section py-5" style="background: #f8f9fa;">
    <div class="container">
        <div class="text-center mb-5">
            <h2 style="font-size: 2.5rem; font-weight: 700; color: #1a1a1a; margin-bottom: 20px;">
                AI Tutor for Every Class
            </h2>
            <p style="font-size: 1.2rem; color: #666; max-width: 600px; margin: 0 auto;">
                Instant doubt solving, PYQs, notes, and exam prep -all in one place.
            </p>
        </div>

        <div id="categoriesContent">
            <!-- Categories will be dynamically generated here -->
        </div>
    </div>
</section>

<!-- Section Separator -->
<section class="separator-section py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; overflow: hidden;">
    <!-- Background Pattern -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1;">
        <div style="position: absolute; top: 20%; left: 10%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%); border-radius: 50%;"></div>
        <div style="position: absolute; top: 60%; right: 15%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>
    </div>

    <div class="container text-center" style="position: relative; z-index: 2;">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h2 style="font-size: 2.5rem; font-weight: 700; color: white; margin-bottom: 1rem; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    Specialized Solutions
                </h2>
                <p style="font-size: 1.2rem; color: rgba(255,255,255,0.9); margin-bottom: 0; line-height: 1.6;">
                    Tailored AI-powered educational solutions for institutions and publishers
                </p>
            </div>
        </div>
    </div>
</section>

<!-- For Institutions and Publishers Section -->
<section class="solutions-section py-5" style="background: #f8f9fa;">
    <div class="container">
        <div class="row">
            <!-- For Institutions -->
            <div class="col-lg-6 mb-5">
                <div class="solution-card h-100" style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <div class="d-flex align-items-center mb-4">
                        <div style="width: 60px; height: 60px; background: #4285f4; border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-right: 20px;">
                            <i class="fas fa-graduation-cap" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <h3 style="font-weight: 700; color: #1a1a1a; margin: 0;">For Institutions</h3>
                    </div>
                    <p style="font-size: 1.1rem; color: #666; margin-bottom: 25px; line-height: 1.6;">
                        Custom testbooks, teacher tools, and analytics for smarter classrooms.
                    </p>
                    <a href="/ibookgpt/features" class="btn" style="background: #4285f4; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: all 0.3s ease;"
                       onmouseover="this.style.background='#3367d6'"
                       onmouseout="this.style.background='#4285f4'">
                        Learn More
                    </a>
                </div>
            </div>

            <!-- For Publishers -->
            <div class="col-lg-6 mb-5">
                <div class="solution-card h-100" style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <div class="d-flex align-items-center mb-4">
                        <div style="width: 60px; height: 60px; background: #ff9500; border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-right: 20px;">
                            <i class="fas fa-book-open" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <h3 style="font-weight: 700; color: #1a1a1a; margin: 0;">For Publishers</h3>
                    </div>
                    <p style="font-size: 1.1rem; color: #666; margin-bottom: 25px; line-height: 1.6;">
                        Enable your books with AI. Sell smarter, reach wider.
                    </p>
                    <a href="/ibookgpt/printBookBundling" class="btn" style="background: #ff9500; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: all 0.3s ease;"
                       onmouseover="this.style.background='#e6850e'"
                       onmouseout="this.style.background='#ff9500'">
                        Learn More
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>


<script>
    // Get categories data from session
    var activeCategoriesSyllabus = [];
    try {
        var sessionData = "${session["activeCategoriesSyllabus_"+session["siteId"]] ?: '[]'}";
        console.log("Raw session data:", sessionData);

        if (sessionData && sessionData !== 'null' && sessionData !== '[]') {
            activeCategoriesSyllabus = JSON.parse(sessionData.replace(/&quot;/g,'"').replace(/&#92;u0026/g,'&'));
            console.log("Parsed categories data:", activeCategoriesSyllabus);

            // Filter out any items with null values
            activeCategoriesSyllabus = activeCategoriesSyllabus.filter(function(item) {
                return item && item.level && item.syllabus;
            });
            console.log("Filtered categories data:", activeCategoriesSyllabus);
        }
    } catch(e) {
        console.log("Error parsing categories data:", e);
        activeCategoriesSyllabus = [];
    }

    // Debug: Log final data
    console.log("Final activeCategoriesSyllabus:", activeCategoriesSyllabus);

    // Initialize categories section
    function initCategories() {
        const categoriesData = activeCategoriesSyllabus;
        console.log("initCategories called with data:", categoriesData);

        if (!categoriesData || categoriesData.length === 0) {
            console.log("No categories data, showing default categories");
            // Fallback to default categories if no data available
            showDefaultCategories();
            return;
        }

        // Group categories by level with null checks
        const groupedCategories = categoriesData.reduce((acc, item) => {
            // Skip items with null or undefined values
            if (!item || !item.level || !item.syllabus) {
                console.log("Skipping invalid item:", item);
                return acc;
            }

            if (!acc[item.level]) {
                acc[item.level] = [];
            }
            if (acc[item.level].indexOf(item.syllabus) === -1) {
                acc[item.level].push(item.syllabus);
            }
            return acc;
        }, {});

        console.log("Grouped categories:", groupedCategories);

        const categoriesContainer = document.getElementById('categoriesContent');
        if (!categoriesContainer) {
            console.log("Categories container not found");
            return;
        }

        let categoriesHTML = '';

        // Generate HTML for each category group (same as GPT Sir)
        Object.keys(groupedCategories).forEach(level => {
            console.log("Processing level:", level);

            // Skip if level is null or empty
            if (!level || level.trim() === '') {
                console.log("Skipping empty level");
                return;
            }

            const syllabuses = groupedCategories[level];
            console.log("Syllabuses for", level, ":", syllabuses);

            if (!syllabuses || syllabuses.length === 0) {
                console.log("No syllabuses for level:", level);
                return;
            }

            const levelEncoded = encodeURIComponent(level);

            categoriesHTML += '<div class="category-group">';
            categoriesHTML += '<a href="/istore?siteName=${session["siteName"]}&level=' + levelEncoded + '" class="category-title">' + level + '</a>';
            categoriesHTML += '<div class="category-cards">';

            // Show only first 3 cards initially
            const visibleCards = syllabuses.slice(0, 3);
            const hasMore = syllabuses.length > 3;

            console.log("Visible cards for", level, ":", visibleCards);

            visibleCards.forEach(syllabus => {
                // Skip if syllabus is null or empty
                if (!syllabus || syllabus.trim() === '') {
                    console.log("Skipping empty syllabus");
                    return;
                }

                const levelUrl = encodeURIComponent(level);
                const syllabusUrl = encodeURIComponent(syllabus);
                const url = '/istore?siteName=${session["siteName"]}&level=' + levelUrl + '&syllabus=' + syllabusUrl;

                categoriesHTML += '<a href="' + url + '" class="category-card">';
                categoriesHTML += '<h4 class="category-card-title">' + syllabus + '</h4>';
                categoriesHTML += '</a>';
            });

            categoriesHTML += '</div>';

            // Add "Explore More" link if there are more than 3 cards
            if (hasMore) {
                const exploreUrl = '/istore?siteName=${session["siteName"]}&level=' + levelEncoded;
                categoriesHTML += '<div class="explore-more">';
                categoriesHTML += '<a href="' + exploreUrl + '" class="explore-more-link">Explore More</a>';
                categoriesHTML += '</div>';
            }

            categoriesHTML += '</div>';
        });

        console.log("Final HTML:", categoriesHTML);
        categoriesContainer.innerHTML = categoriesHTML;

        // Add scroll animations
        animateCategoriesOnScroll();
    }

    // Animate categories on scroll
    function animateCategoriesOnScroll() {
        const categoryGroups = document.querySelectorAll('.category-group');

        function checkCategoriesInView() {
            categoryGroups.forEach((group, index) => {
                if (isInViewport(group)) {
                    setTimeout(() => {
                        group.style.opacity = '1';
                        group.style.transform = 'translateY(0)';
                    }, index * 200);
                }
            });
        }

        // Helper function to check if element is in viewport
        function isInViewport(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                rect.bottom >= 0
            );
        }

        // Initial setup
        categoryGroups.forEach(group => {
            group.style.opacity = '0';
            group.style.transform = 'translateY(40px)';
            group.style.transition = 'all 0.6s ease';
        });

        // Check on scroll
        window.addEventListener('scroll', checkCategoriesInView);
        checkCategoriesInView(); // Check initially
    }

    function showDefaultCategories() {
        // Fallback categories if no session data
        const defaultCategories = [
            { level: 'School', icon: 'fas fa-graduation-cap' },
            { level: 'College', icon: 'fas fa-university' },
            { level: 'Competitive Exams', icon: 'fas fa-trophy' },
            { level: 'Professional', icon: 'fas fa-briefcase' }
        ];

        const categoriesContainer = document.getElementById('categoriesContent');
        if (!categoriesContainer) return;

        let categoriesHTML = "<div class='row justify-content-center'>";

        defaultCategories.forEach((category, index) => {
            const cardStyles = [
                { bg: '#faf7f2', border: '#f0ebe0', gradient: 'linear-gradient(135deg, #667eea, #764ba2)' },
                { bg: '#f2f7fa', border: '#e0ebf0', gradient: 'linear-gradient(135deg, #4285f4, #667eea)' },
                { bg: '#fff9f2', border: '#f0e6d2', gradient: 'linear-gradient(135deg, #ff9500, #ff6b35)' },
                { bg: '#f2faf7', border: '#d8f0e0', gradient: 'linear-gradient(135deg, #34a853, #4caf50)' }
            ];

            const style = cardStyles[index % cardStyles.length];
            const url = "/ebooks?level=" + encodeURIComponent(category.level);

            categoriesHTML += "<div class='col-lg-3 col-md-6 mb-4'>";
            categoriesHTML += "<a href='" + url + "' style='text-decoration: none;'>";
            categoriesHTML += "<div class='class-card' style='background: " + style.bg + "; padding: 40px 30px; border-radius: 20px; text-align: center; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(0,0,0,0.08); border: 1px solid " + style.border + "; height: 200px; display: flex; flex-direction: column; justify-content: center;'";
            categoriesHTML += " onmouseover=\"this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.12)'\"";
            categoriesHTML += " onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.08)'\">";

            // Icon
            categoriesHTML += "<div style='width: 70px; height: 70px; background: " + style.gradient + "; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; box-shadow: 0 6px 15px rgba(0,0,0,0.3);'>";
            categoriesHTML += "<i class='" + category.icon + "' style='color: white; font-size: 1.8rem;'></i>";
            categoriesHTML += "</div>";

            // Content
            categoriesHTML += "<h3 style='font-weight: 700; margin-bottom: 0; color: #2c3e50; font-size: 1.4rem;'>" + category.level + "</h3>";
            categoriesHTML += "</div>";
            categoriesHTML += "</a>";
            categoriesHTML += "</div>";
        });

        categoriesHTML += "</div>";
        categoriesContainer.innerHTML = categoriesHTML;
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initCategories();
    });
</script>

<style>
/* Categories Section Styles - Same as GPT Sir */
.category-group {
    margin-bottom: 50px;
}

.category-group:last-child {
    margin-bottom: 0;
}

.category-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 24px;
    text-align: left;
    position: relative;
    padding-bottom: 8px;
    display: flex;
    justify-content: flex-start;
    text-decoration: none;
}

.category-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #4285f4, #667eea);
    border-radius: 2px;
}

.category-title:hover {
    color: #4285f4;
    text-decoration: none;
}

.category-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
    max-width: 900px;
}

.category-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 2px solid #f1f5f9;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    display: block;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    border-color: #4285f4;
    text-decoration: none;
    color: inherit;
}

.category-card:hover::before {
    background: linear-gradient(90deg, #4285f4, #667eea);
}

.category-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    text-align: center;
    line-height: 1.4;
}

.explore-more {
    text-align: right;
    margin-top: 16px;
}

.explore-more-link {
    color: #4285f4;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.explore-more-link:hover {
    color: #3367d6;
    text-decoration: none;
    transform: translateX(4px);
}

.explore-more-link::after {
    content: '→';
    font-size: 16px;
    transition: transform 0.3s ease;
}

.explore-more-link:hover::after {
    transform: translateX(2px);
}

/* Responsive Design */
@media (max-width: 1024px) and (min-width: 769px) {
    .category-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }
}

@media (max-width: 768px) {
    .category-group {
        margin-bottom: 40px;
    }

    .category-title {
        font-size: 22px;
        margin-bottom: 20px;
    }

    .category-cards {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 16px;
    }

    .category-card {
        padding: 20px 16px;
    }

    .category-card-title {
        font-size: 16px;
    }
}
</style>

<%-- Include discount popup modal --%>
<g:render template="/ibookgpt/discountPopup"></g:render>

<script>
document.addEventListener("DOMContentLoaded", function() {
    <g:if test="${showDiscountPopup}">
        // Show discount popup after a short delay
        setTimeout(function() {
            showDiscountPopup("${discountCode}", "${discountName ?: ''}");
        }, 1000);
    </g:if>
});
</script>

<g:render template="/privatelabel/footer_new"></g:render>
