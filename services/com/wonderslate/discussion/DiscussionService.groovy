package com.wonderslate.discussion

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.SubjectMst
import com.wonderslate.discussions.DiscussionAnswers
import com.wonderslate.discussions.DiscussionAnswersProperties
import com.wonderslate.discussions.DiscussionBoardUser
import com.wonderslate.discussions.DiscussionBoardUserPoints
import com.wonderslate.discussions.DiscussionLevelDtl
import com.wonderslate.discussions.DiscussionPointsMst
import com.wonderslate.discussions.DiscussionQuestionsProperties
import com.wonderslate.discussions.DiscussionQuestionsUserFollowing
import com.wonderslate.discussions.DiscussionQuestions
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.UserPointsService
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.bson.conversions.Bson
import org.imgscalr.Scalr
import org.springframework.web.multipart.MultipartFile

import javax.imageio.ImageIO
import java.awt.image.BufferedImage
import java.nio.file.Files
import java.sql.Date
import java.sql.Timestamp
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.Instant

import org.bson.types.ObjectId
import org.bson.Document

@Transactional
class DiscussionService {
    def grailsApplication
    def mongo
    def springSecurityService
    DiscussionBoardCacheService discussionBoardCacheService
    DataProviderService dataProviderService
    AsyncLogsService asyncLogsService
    MailService mailService
    UserManagementService userManagementService
    UserPointsService userPointsService
    Integer discussionPointsMstIdForAddingQuestion = 1
    Integer discussionPointsMstIdForAddingAnswer = 2
    Integer discussionPointsMstIdForUpvotingQuestion = 3
    Integer discussionPointsMstIdForUpvotingAnswer = 4
    Integer discussionPropertiesMstIdForAbuse = 2
    Integer discussionPropertiesMstIdForIncorrect = 1
    Integer discussionPropertiesMstIdForConfirmIncorrect = 3
    Integer discussionPropertiesMstIdForConfirmAbus = 4
    def redisService
    def serviceMethod() {

    }

    def addAnswer(params,long _id, String answerText, int siteId,  MultipartFile file,Boolean img,def clientName, def siteName,def questionLink){
        def discussionQuestion = DiscussionQuestions.findById(_id)
        Integer pointsForAddingAnswer = DiscussionPointsMst.findById(discussionPointsMstIdForAddingAnswer).getPointsValue()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        String userId = "", userName = ""
        if(user != null) {
            userId = user.username
            userName = user.name
        }
        String adminUser = ""
        if (user != null && user.authorities.any {
            it.authority == "ROLE_WS_CONTENT_ADMIN"
        }) {
            adminUser = "true";
        }
       if(true){ //discussionQuestion.archive == null
//           Date date= new Date();
//           long time = date.getTime();
        DiscussionAnswers discussionAnswers1 = new DiscussionAnswers()
//        discussionAnswers = discussionQuestion.qaList
//           if(discussionAnswers == null) {
//               discussionAnswers = new ArrayList<>()
//           }
      /*  if(discussionAnswers == null){
            discussionAnswers = new ArrayList<>()
            discussionQuestion.highAnsVote = 0
            discussionQuestion.highAnswer = request.JSON.answer
            discussionQuestion.highUserId = user.username
            discussionQuestion.highUserName = user.name
            discussionQuestion.highAnswerId = answerId
        }else if(discussionAnswers.size() > 0){
            int upvote = request.JSON.upVote
            if(discussionQuestion.highAnsVote < upvote){
                discussionQuestion.highAnsVote = request.JSON.upVote
                discussionQuestion.highAnswer = request.JSON.answer
                discussionQuestion.highUserId = user.username
                discussionQuestion.highUserName = user.name
                discussionQuestion.highAnswerId = answerId
            }
//            if(discussionAnswers.size() > 50){
//
//            }
        } */
        discussionAnswers1.answer = answerText
        discussionAnswers1.discussionQuestionId = _id
        discussionAnswers1.upVoteCount = 0
        discussionAnswers1.createdBy = userId
        if(!adminUser.equals("true")) discussionAnswers1.showAnswer = false
        else discussionAnswers1.showAnswer = true
           if(discussionQuestion.dldId!=null) discussionAnswers1.showAnswer = true
//        if(user != null && user.profilepic != null) {
//            discussionAnswers1.uImgId = user.id
//            discussionAnswers1.uImgName = user.profilepic
//        }
        if(img == true){
            discussionAnswers1.save(flush:true)
               File uploadDir = new File("upload/discussionDoubt/"+discussionQuestion.id+"/answers/"+discussionAnswers1.id)
               if(!uploadDir.exists()) uploadDir.mkdirs()

               //creating directory to process images
               File uploadDir1 = new File(uploadDir.absolutePath+"/answers-processed")
               if(!uploadDir1.exists()) uploadDir1.mkdirs()
               String filename=file.originalFilename
               filename=filename.replaceAll("\\s+","")
               BufferedImage image = ImageIO.read(file.getInputStream())

               ByteArrayOutputStream baos = new ByteArrayOutputStream()
               ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
               baos.flush()
               byte[] scaledImageInByte = baos.toByteArray()
               baos.close()

               baos = new ByteArrayOutputStream()
               ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
               baos.flush()
               byte[] scaledImageInByte1 = baos.toByteArray()
               baos.close()

               baos = new ByteArrayOutputStream()
               ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
               baos.flush()
               byte[] scaledImageInByte2 = baos.toByteArray()
               baos.close()

               FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
               FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
               FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

               //saving original image finally
            file.transferTo(new File(uploadDir.absolutePath+"/"+file.filename))
               discussionAnswers1.imgName = file.filename
           }
//        discussionAnswers.add(discussionAnswers1)
//        discussionQuestion.qaList = discussionAnswers
           discussionAnswers1.save(flush:true)
           List discussinQuestionProperties = DiscussionQuestionsProperties.findAllByDiscussionQuestionIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForIncorrect)
           for(DiscussionQuestionsProperties questionsProperties:discussinQuestionProperties){
               questionsProperties.delete(flush:true)
           }
           discussinQuestionProperties = DiscussionQuestionsProperties.findAllByDiscussionQuestionIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForAbuse)
           for(DiscussionQuestionsProperties questionsProperties:discussinQuestionProperties){
               questionsProperties.delete(flush:true)
           }
           def discussionUsers = null
        if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(userId)
//        if(discussionUsers != null){
//            List userAnsweredAuestionIds = null
//            List userQuestionIds = null
//            if(discussionUsers.answeredIds == null ) userAnsweredAuestionIds = new ArrayList()
//            else userAnsweredAuestionIds = discussionUsers.answeredIds
//            if(discussionUsers.questionIds == null ) userQuestionIds = new ArrayList()
//            else userQuestionIds = discussionUsers.questionIds
//            userAnsweredAuestionIds.add(answerId)
//            discussionUsers.answeredIds = userAnsweredAuestionIds
//
//            discussionUsers['score'] = (userAnsweredAuestionIds.size() * 10) + (userQuestionIds.size() * 5)
//            discussionUsers.save(flush:true)
//        }else if(user != null){
//            DiscussionUsers discussionNewUser = new DiscussionUsers()
//            List answeredIds = new ArrayList();
//            answeredIds.add(answerId)
//            discussionNewUser.userid = user.username
//            discussionNewUser.userName = user.name
//            discussionNewUser.totalUpVoteCount = 0
//            discussionNewUser.answeredIds = answeredIds
//            discussionNewUser.save(flush:true)
//        }
           if(discussionUsers != null){
               discussionUsers.setUserTotalPoints(discussionUsers.getUserTotalPoints() + pointsForAddingAnswer)
           }else {
               discussionUsers = new DiscussionBoardUser()
               discussionUsers.userId = userId
               discussionUsers.userTotalPoints = pointsForAddingAnswer
           }
           discussionUsers.save(flush:true)
           DiscussionBoardUserPoints discussionBoardUserPoints = new DiscussionBoardUserPoints()
           discussionBoardUserPoints.userId = userId
           discussionBoardUserPoints.discussionPointsId = discussionPointsMstIdForAddingAnswer
           discussionBoardUserPoints.answerId = discussionAnswers1.id
           discussionBoardUserPoints.save(flush:true)
        discussionQuestion.showQuestion = true
//        if(qEmail == null || qEmail.isEmpty() || !qEmail.contains("@")) {
//            if(user != null && user.email != null && !user.email.isEmpty()) discussionQuestion.email = user.email
//            else discussionQuestion.email = "email"
//        }
        discussionQuestion.save(flush:true)
//           Gson gson = new Gson();
//           String element = gson.toJson(discussionAnswers1)
//           def database = mongo.getDatabase("wsdiscussion");
//           String comand ="{" +
//                   "      update: \"discussionQuestions\"," +
//                   "      updates: [" +
//                   "         {" +
//                   "           q: {\"_id\": ObjectId(\"" +
//                   _id +
//                   "\")}, u: {  \$push:{\"qaList\":"+element+" }}" +
//                   "         }" +
//                   "      ]" +
//                   "   }"
//           Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
           if(user.email != null && userManagementService.validateEmail(user.email,siteId)) {
          sendEmail(user.email,user.name, "Your Answer is Submitted to review. Once approved You will be notified","Answer Submitted", siteId, siteName,clientName, questionLink)
           }
           User Quser = dataProviderService.getUserMst(discussionQuestion.createdBy)
        if(Quser.email != null && !Quser.email.isEmpty() && adminUser.equals("true")) sendEmail(Quser.email,Quser.name, "Your Question has been Answered.", "Question Answered", siteId, siteName,clientName, questionLink)
           if(params.batchId!=null && !"".equals(params.batchId) && params.instituteId!=null && !"".equals(params.instituteId)) {
               redisService.("recentAnswer_" + params.batchId) = null
               redisService.("recentAnswer_" + params.instituteId) = null
           }else if(params.batchId!=null && !"".equals(params.batchId)){
               redisService.("recentAnswer_" + params.batchId) = null
           }else if(params.instituteId!=null && !"".equals(params.instituteId)){
               redisService.("recentAnswer_" + params.instituteId) = null
           }else{
               discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,siteId,0)
               discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,0)
               discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,0)
           }
        discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(siteId,0)
        discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(siteId)
           discussionBoardCacheService.updateDiscussionAbuseAnswersCache(siteId)
           discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(siteId)
//        discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(siteId,0)
//           if(discussionQuestion.qaList.size() == 1){
               discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(siteId,0)
//           }
           return discussionQuestion;
       }else{
           return ['error':'Question is archived. Answer cannot be added to the archived question']
       }
    }

    def addDiscussionQuestions(params,String title, String question, int siteId, long bookId, long chapterId ,String tags, MultipartFile file,Boolean free, Boolean img,def clientName,def siteName,def serverUrl){
        // userId from spring security
        User user = null
        Integer pointsForAddingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForAddingQuestion).getPointsValue()
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        def user = [username:'<EMAIL>',name:'Demo User',id:123,profilepic:'safsdf']
        String userId = ""
        if(user != null) userId = user.username
        // get resId and update free variable
        if(bookId > 0) {
            BooksMst booksMst = dataProviderService.getBooksMst(bookId)
            if(booksMst != null && booksMst.price != null && booksMst.price > 0){
                if(chapterId > 0){
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
                    if(chaptersMst != null && chaptersMst.previewChapter.equals("true")) free = true
                    else free = false
                }else free = false

            }else free = true

        } else free = true
        if(tags == null) {
            tags = ""
        }
        DiscussionQuestions discussionQuestions = new DiscussionQuestions()
        discussionQuestions.upvoteCount = 0
        discussionQuestions.question = question
        discussionQuestions.tags = tags
        discussionQuestions.free = free
        if (user != null && user.authorities.any {
            it.authority == "ROLE_WS_CONTENT_ADMIN"
        }) {
            discussionQuestions.showQuestion = true
        }else discussionQuestions.showQuestion = false
        discussionQuestions.siteId = siteId
        if(user != null) discussionQuestions.createdBy = userId
//        if(user != null && user.email != null) discussionQuestions.email = user.email
//        else discussionQuestions.email = ""
        if(params.batchId!=null && !"".equals(params.batchId)){
            DiscussionLevelDtl discussionLevelDtl =DiscussionLevelDtl.findByBatchId(new Long(params.batchId))
            if(discussionLevelDtl!=null){
                discussionQuestions.showQuestion =true
                discussionQuestions.dldId = discussionLevelDtl.id
            }
        }else if(params.instituteId!=null && !"".equals(params.instituteId)){
            DiscussionLevelDtl discussionLevelDtl =DiscussionLevelDtl.findByInstituteId(new Long(params.instituteId))
            if(discussionLevelDtl!=null){
                discussionQuestions.showQuestion =true
                discussionQuestions.dldId = discussionLevelDtl.id
            }
        }
        discussionQuestions.save(flush:true)
        String questionLink = serverUrl + "/discussion/discussionBoard?qId="+discussionQuestions.id+"&open=questionExplorer"
        if( user != null && user.email != null && !user.authorities.any {it.authority == "ROLE_WS_CONTENT_ADMIN"} && params.instituteId==null) sendEmail(user.email,user.name, "Your Question has been sent to review. Once approved you’ll be notified.","Question Submitted", siteId, siteName,clientName, questionLink)
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)


        if(img == true){
            File uploadDir = new File("upload/discussionDoubt/"+discussionQuestions.id)
            if(!uploadDir.exists()) uploadDir.mkdirs()

            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
            if(!uploadDir1.exists()) uploadDir1.mkdirs()
            String filename=file.originalFilename
            filename=filename.replaceAll("\\s+","")
            BufferedImage image = ImageIO.read(file.getInputStream())

            ByteArrayOutputStream baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte = baos.toByteArray()
            baos.close()

            baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte1 = baos.toByteArray()
            baos.close()

            baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte2 = baos.toByteArray()
            baos.close()

            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

            //saving original image finally
            file.transferTo(new File(uploadDir.absolutePath+"/"+file.filename))
            discussionQuestions.imgName = file.filename
            discussionQuestions.save(flush:true)
        }
        if(tags == null){
            discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(siteId,0)
        }
        if(params.batchId!=null && !"".equals(params.batchId) && params.instituteId!=null && !"".equals(params.instituteId)) {
            redisService.("recentAnswer_" + params.batchId) = null
            redisService.("recentAnswer_" + params.instituteId) = null
        }else if(params.batchId!=null && !"".equals(params.batchId)){
            redisService.("recentAnswer_" + params.batchId) = null
        }else if(params.instituteId!=null && !"".equals(params.instituteId)){
            redisService.("recentAnswer_" + params.instituteId) = null
        }
        else{
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.getRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(siteId,0)
        }

        def discussionUsers = null
        if(user != null) discussionUsers = DiscussionBoardUser.findByUserId(userId)
//        if(discussionUsers != null){
//            List userQuestionIds = null
//            List userAnsweredAuestionIds = null
//            if(discussionUsers.questionIds == null ) userQuestionIds = new ArrayList()
//            else userQuestionIds = discussionUsers.questionIds
//            if(discussionUsers.answeredIds == null ) userAnsweredAuestionIds = new ArrayList()
//            else userAnsweredAuestionIds = discussionUsers.answeredIds
//            userQuestionIds.add(discussionQuestions.id)
//            discussionUsers.questionIds = userQuestionIds
//            //create method for calculating user score
//            discussionUsers['score'] = (userAnsweredAuestionIds.size() * 10) + (userQuestionIds.size() * 5)
//            discussionUsers.save(flush:true)
//        }else if(user != null){
//            DiscussionUsers discussionNewUser = new DiscussionUsers()
//            List userQuestionIds = new ArrayList();
//            userQuestionIds.add(discussionQuestions.id)
//            discussionNewUser.userid = userId
//            discussionNewUser.userName = user.name
//            discussionNewUser.totalUpVoteCount = 0
//            discussionNewUser.questionIds = userQuestionIds
//            discussionNewUser.save(flush:true)
//        }

        if(discussionUsers != null){
            discussionUsers.setUserTotalPoints(discussionUsers.getUserTotalPoints() + pointsForAddingQuestion)
        }else {
            discussionUsers = new DiscussionBoardUser()
            discussionUsers.userId = userId
            discussionUsers.userTotalPoints = pointsForAddingQuestion
        }
        discussionUsers.save(flush:true)
        DiscussionBoardUserPoints discussionBoardUserPoints = new DiscussionBoardUserPoints()
        discussionBoardUserPoints.userId = userId
        discussionBoardUserPoints.discussionPointsId = pointsForAddingQuestion
        discussionBoardUserPoints.questionId = discussionQuestions.id
        discussionBoardUserPoints.save(flush:true)


//        def database = mongo.getDatabase("wsdiscussion");
//        String comand ="{" +
//                "      update: \"discussionUsers\"," +
//                "      updates: [" +
//                "         {" +
//                "           q: {\"userid\": \"" +
//                userId +
//                "\"}, u: {  \$push:{\"questionsFollowing\":ObjectId(\""+discussionQuestions.id+"\") }}" +
//                "         }" +
//                "      ]" +
//                "   }"
//        Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
//        SimpleDateFormat f = new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss z");
//        comand ="{" +
//                "      update: \"discussionQuestions\"," +
//                "      updates: [" +
//                "         {" +
//                "           q: {\"_id\": ObjectId(\"" +
//                discussionQuestions.id +
//                "\")}, u: {  \$push:{\"qaList\":{\"createdOn\": new Date(\""+f.format(discussionQuestions.id.date)+"\")} }}" +
//                "         }" +
//                "      ]" +
//                "   }"
//        buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
       return discussionQuestions
    }

    def moderateQuestion(params,def request,def clientName,def siteName,def serverUrl){
        def _id = request.JSON.id//new ObjectId(request.JSON.timestamp, request.JSON.machineIdentifier, (short) request.JSON.processIdentifier, request.JSON.counter)
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){//discussionQuestion.archive == null
            discussionQuestion.showQuestion = true;
            discussionQuestion.save(flush:true)
            User questionCreatedUser = User.findByUsername(discussionQuestion.createdBy)
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")}, u: {  \$set:{\"show\":true}}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
            String questionLink = serverUrl + "/discussion/discussionBoard?qId="+request.JSON.timestamp+"__"+request.JSON.machineIdentifier+"__"+request.JSON.processIdentifier+"__"+request.JSON.counter+"&open=questionExplorer"
            if(questionCreatedUser != null && questionCreatedUser.email != null && !questionCreatedUser.email.isEmpty()) sendEmail(questionCreatedUser.email,questionCreatedUser.name, "Your Question Approved.","Question Approved", Integer.parseInt(request.JSON.siteId), siteName,clientName, questionLink)
            discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            return ['questionsEdited':discussionQuestion]
        }else {
            return ['error':'Question is archived. Question cannot be moderated']
        }
    }

    def getUnModeratedDiscussionQuestions(def siteId, int batchIndex){
        List questions = discussionBoardCacheService.getUnModeratedDiscussionQuestionCache(siteId,batchIndex)
        if(questions == null) questions = new ArrayList()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
        List sendQuestions =  questions.each { def q ->
            def _id = q['_id']
            User uQ = User.findByUsername(q['createdBy'])
            q['userId'] = q['createdBy']
            q['userName'] = uQ.name
            q['uImgId'] = uQ.id
            q['uImgName'] = uQ.profilepic
            q['qImgId'] = _id
            q['qImgName'] = q['img_name']
//            if(qVotes != null && qVotes.size() > 0){
//                if(qVotes.indexOf(_id) > -1) {
//                    q["voted"] = true
//                }
//            }
            if(user != null && DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,user.username) != null){
                q["voted"] = true
            }
            java.util.Date toDay = new java.util.Date()
            long timeStampMillis = toDay.getTime();
            DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
            java.util.Date qDate = format.parse(q['dateCreated']);
            println(qDate.getTime())
            println(timeStampMillis - qDate.getTime())
            if(timeStampMillis - qDate.getTime() <= 86400000) {
                double hrs = (timeStampMillis - qDate.getTime())/(60 * 60 * 1000)
                if(hrs < 1) hrs = 1
                q["hours"] = (int)hrs
            }
//            if(batchIndex == 0) q['_id'].date = _id.date
            return q
        }
        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions;
    }

    def getAdminAnswersTabList(def siteId, String tab,String keyWord,String type){
        List questions = null
        if(tab.equals("UMA")) questions = discussionBoardCacheService.getUnModeratedDiscussionAnswersCache(siteId)
        else if(tab.equals("RAA")) questions = discussionBoardCacheService.getDiscussionAbuseAnswersCache(siteId)
        else if(tab.equals("RIA")) questions = discussionBoardCacheService.getDiscussionIncorrectAnswersCache(siteId)
        if(questions == null) questions = new ArrayList()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
        List sendQuestions =  new ArrayList()
             questions.each { def q ->
            if(!type.equals("search")){
                Long _id = q['_id'] //new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
            if(user != null && DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,user.username) != null){
                    q["voted"] = true
            }
                User uQ = User.findByUsername(q.answersList[0]['createdBy'])
                q['userId'] = q.answersList[0]['createdBy']
                q['userName'] = uQ.name
                q['uImgId'] = uQ.id
                q['uImgName'] = uQ.profilepic
                q['qImgId'] = _id
                q['qImgName'] = q.answersList[0]['img_name']
                java.util.Date toDay = new java.util.Date()
                long timeStampMillis = toDay.getTime();
                DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
                java.util.Date qDate = format.parse(q.answersList[0]['dateCreated']);
//            Date qDate = q['dateCreated']
                if(timeStampMillis - qDate.getTime() <= 86400) {
                    double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                    if(hrs < 1) hrs = 1
                    q["hours"] = (int)hrs
                }

            List answerList = q.answersList
            List sendAnswerList = new ArrayList()
                    answerList.each { ans ->
//                        if(uVoted !=null && uVoted.size() > 0 && uVoted.indexOf(user.username) > -1)
                        uQ = User.findByUsername(ans.qaList['created_by'])
                        ans['userId'] = ans.qaList['created_by']
                        ans['userName'] = uQ.name
                        ans['uImgId'] = uQ.id
                        ans['uImgName'] = uQ.profilepic
//                        anslist.add(ans)
                        if(user != null && DiscussionBoardUserPoints.findByAnswerIdAndDiscussionPointsIdAndUserId(ans.qaList['id'],discussionPointsMstIdForUpvotingAnswer,user.username) != null) ans.qaList['voted'] = true
                        if(keyWord == null || keyWord.isEmpty()) {
                            sendAnswerList.add(ans)
                        }
                        else{
                            String question = ans['question']
                            String userName = ans['userName']
                            String answer = ans['qaList'].answer
                            if((question.toLowerCase().replaceAll(System.lineSeparator(),"").contains(keyWord) && questions.indexOf(question) == -1)
                                    || ( userName != null && userName.toLowerCase().contains(keyWord) && questions.indexOf(userName) == -1)
                                    || ( answer != null && answer.toLowerCase().replaceAll(System.lineSeparator(),"").contains(keyWord) && questions.indexOf(answer) == -1)) {
                                sendAnswerList.add(ans)
//                                q.answersList = sendAnswerList
//                                sendQuestions.add(q)
                            }
                        }
            }
                if(sendAnswerList.size() >0){
                    q.answersList = sendAnswerList
                    sendQuestions.add(q)
                }
            }else if(keyWord != null && !keyWord.isEmpty()){
                List answerList = q.answersList
                List sendAnswerList = new ArrayList()
                answerList.each { ans ->
                        String question = ans['question']
                        String userName = ans['userName']
                        String answer = ans['qaList'].answer
                        if(question.toLowerCase().replaceAll(System.lineSeparator(),"").contains(keyWord) && questions.indexOf(question) == -1) sendQuestions.add(question)
                        if(userName != null && userName.toLowerCase().contains(keyWord) && questions.indexOf(userName) == -1) sendQuestions.add(userName)
                        if(answer != null && answer.toLowerCase().replaceAll(System.lineSeparator(),"").contains(keyWord) && questions.indexOf(answer) == -1) sendQuestions.add(answer)
                }
            }
        }
        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions;
    }

    def unModeratedQuestionSearchSuggestion(params,def siteId, def searchkeyWord, String type,String tab){

        List<String> questions = new ArrayList<>()
        List cacheQuestions = new ArrayList()
        if(tab.equals("UM")) cacheQuestions = discussionBoardCacheService.getUnModeratedDiscussionQuestionCache(siteId,0)
        else if(tab.equals("UA")) cacheQuestions = discussionBoardCacheService.getUnAnsweredDiscussionQuestionCache(siteId,0)
        else if(tab.equals("RA")) cacheQuestions = discussionBoardCacheService.getAbuseDiscussionQuestionCache(params,siteId,0)
        else if(tab.equals("RI")) cacheQuestions = discussionBoardCacheService.getIncorrectDiscussionQuestionCache(params,siteId,0)
        else if(tab.equals("UT")) cacheQuestions = discussionBoardCacheService.getUnTagedDiscussionQuestionCache(siteId,0)

        if(cacheQuestions == null) cacheQuestions = new ArrayList()
        if(type.equals("search")){
            cacheQuestions.each { q ->
            String question = q['question']
            String userName = q['userName']
             if(question.toLowerCase().contains(searchkeyWord)) {
                 questions.add(question)
             }else if(userName != null && userName.toLowerCase().contains(searchkeyWord)){
                 questions.add(userName)
             }
//            if(question.toLowerCase().contains(searchkeyWord) || userName.toLowerCase().contains(searchkeyWord)) questions.add(question + " By" + userName)
        }
        }else if(type.equals("questions")){
            cacheQuestions.each { q ->
                def _id = q['_id']//new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
                User uQ = User.findByUsername(q['createdBy'])
                q['userId'] = q['createdBy']
                q['userName'] = uQ.name
                q['uImgId'] = uQ.id
                q['uImgName'] = uQ.profilepic
                q['qImgId'] = _id
                q['qImgName'] = q['img_name']
                String question = q['question']
                String userName = q['userName']
                if(question.toLowerCase().replaceAll(System.lineSeparator(),"").contains(searchkeyWord)) {
                    java.util.Date toDay = new java.util.Date()
                    long timeStampMillis = toDay.getTime();
                    DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
                    java.util.Date qDate = format.parse(q['dateCreated']);

                    if(timeStampMillis - qDate.getTime() <= 86400) {
                        double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                        if(hrs < 1) hrs = 1
                        q["hours"] = (int)hrs
                    }
//                    q['_id'].date = _id.date
                    questions.add(q)
                }else if(userName != null && userName.toLowerCase().contains(searchkeyWord)){
                    java.util.Date toDay = new java.util.Date()
                    long timeStampMillis = toDay.getTime();
                    DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
                    java.util.Date qDate = format.parse(q['dateCreated']);
                    if(timeStampMillis - qDate.getTime() <= 86400) {
                        double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                        if(hrs < 1) hrs = 1
                        q["hours"] = (int)hrs
                    }
//                    q['_id'].date = _id.date
                    questions.add(q)
                }
            }
        }
//        if(questions.size() < 10 || type.equals("questions")) {
//            BasicDBObject questionRegexQuery = new BasicDBObject();
//            questionRegexQuery.put("question", new BasicDBObject("\$regex", searchkeyWord+".*").append("\$options", "i"));
//            BasicDBObject tagRegexQuery = new BasicDBObject();
//            tagRegexQuery.put("tags", new BasicDBObject("\$regex", searchkeyWord+".*").append("\$options", "i"));
//            BasicDBObject titleRegexQuery = new BasicDBObject();
//            titleRegexQuery.put("title", new BasicDBObject("\$regex", searchkeyWord+".*").append("\$options", "i"));
//            BasicDBObject userNameRegexQuery = new BasicDBObject();
//            userNameRegexQuery.put("userName", new BasicDBObject("\$regex", searchkeyWord+".*").append("\$options", "i"));
//            BasicDBList firstOrValues = new BasicDBList()
//            firstOrValues.add( questionRegexQuery )
//            firstOrValues.add( tagRegexQuery )
//            firstOrValues.add( titleRegexQuery )
//            firstOrValues.add( userNameRegexQuery )
//            DBObject firstOr = new BasicDBObject( "\$or", firstOrValues )
//            DBObject freeFilter = new BasicDBObject( "free", true )
//            DBObject moderateFilter = null
//
//            if(tab.equals("UM")) moderateFilter = new BasicDBObject( "show", false )
//            else if(tab.equals("RA")) moderateFilter = new BasicDBObject( "abuse", '0' )
//            else if(tab.equals("RI")) moderateFilter = new BasicDBObject( "incorrect", '0' )
//            else if(tab.equals("UA")) moderateFilter = new BasicDBObject( "show", new BasicDBObject("\$exists", false) )
//            else if(tab.equals("UT")) moderateFilter = new BasicDBObject( "tags", new BasicDBObject("\$size", 0) )
//
//            DBObject siteIdFilter = new BasicDBObject( "siteId", siteId );
//            BasicDBList firstAndValues = new BasicDBList();
//            firstAndValues.add(freeFilter)
//            firstAndValues.add(siteIdFilter)
//            firstAndValues.add(moderateFilter)
//            DBObject firstAnd = new BasicDBObject( "\$and", firstAndValues )
//            BasicDBList finalAndValues = new BasicDBList()
//            finalAndValues.add(firstOr)
//            finalAndValues.add(firstAnd)
//            DBObject query = new BasicDBObject( "\$and", finalAndValues );
//            def db = mongo.getDB("wsdiscussion")
//            def dbObjects = db.getCollection("discussionQuestions").find(query);
//            if(type.equals("search")){
//                dbObjects?.collect {
//                    String question = it['question']
//                    String userName = it['userName']
//                    if(question.toLowerCase().contains(searchkeyWord) && questions.indexOf(question) == -1) {
//                        questions.add(question)
//                    }else if(userName != null && userName.toLowerCase().contains(searchkeyWord) && questions.indexOf(userName) == -1){
//                        questions.add(userName)
//                    }
//                }
//            }else if(type.equals("questions") || type.equals("filter")){
//                dbObjects?.collect {
//                    Boolean match = false
//                    questions.each { q ->
//                        def _id = q['_id']//new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
//                        if(_id.equals(it['_id'])){
//                            match = true
//                        }
//                    }
//                    if(match == false) questions.add(it)
//                }
//            }
//        }
        return questions
    }

    def getUnAnsweredDiscussionQuestion(def siteId,int batchIndex){
        List questions = discussionBoardCacheService.getUnAnsweredDiscussionQuestionCache(siteId,batchIndex)
        if(questions == null) questions = new ArrayList()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
        List sendQuestions =  questions.each { def q ->
            def _id = q['_id'] //new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
            User uQ = User.findByUsername(q['createdBy'])
            q['userId'] = q['createdBy']
            q['userName'] = uQ.name
            q['uImgId'] = uQ.id
            q['uImgName'] = uQ.profilepic
            q['qImgId'] = _id
            q['qImgName'] = q['img_name']
//            if(qVotes != null && qVotes.size() > 0){
//                if(qVotes.indexOf(_id) > -1) {
//                    q["voted"] = true
//                }
//            }
            if(user != null && DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,user.username) != null){
                q["voted"] = true
            }
//            Instant instant = Instant.now();
//            long timeStampMillis = instant.getEpochSecond();
//            if(timeStampMillis - _id.getTimestamp() <= 86400) {
//                double hrs = (timeStampMillis - _id.getTimestamp())/(60 * 60)
//                if(hrs < 1) hrs = 1
//                q["hours"] = (int)hrs
//            }
            java.util.Date toDay = new java.util.Date()
            long timeStampMillis = toDay.getTime();
            DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
            java.util.Date qDate = format.parse(q['dateCreated']);
//            Date qDate = q['dateCreated']
            if(timeStampMillis - qDate.getTime() <= 86400) {
                double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                if(hrs < 1) hrs = 1
                q["hours"] = (int)hrs
            }
//            if(batchIndex == 0) q['_id'].date = _id.date
            return q
        }
        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions
    }

    def getUnTagedDiscussionQuestions(def siteId,int batchIndex){
        List questions = discussionBoardCacheService.getUnTagedDiscussionQuestionCache(siteId,batchIndex)
        if(questions == null) questions = new ArrayList()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
        List sendQuestions =  new ArrayList<>()
        questions.each { def q ->
            def _id = new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
//            if(qVotes != null && qVotes.size() > 0){
//                if(qVotes.indexOf(_id) > -1) {
//                    q["voted"] = true
//                }
//            }
            Instant instant = Instant.now();
            long timeStampMillis = instant.getEpochSecond();
            if(timeStampMillis - _id.getTimestamp() <= 86400) {
                double hrs = (timeStampMillis - _id.getTimestamp())/(60 * 60)
                if(hrs < 1) hrs = 1
                q["hours"] = (int)hrs
            }
            if(batchIndex == 0) q['_id'].date = _id.date
            if(q.qaList!= null){
                List anslist = new ArrayList()
                q.qaList.each{def ans->
                    if(ans['show'] == true) anslist.add(ans)
                }
                q.qaList = anslist
                q.answerCount = anslist.size()
            }
            sendQuestions.add(q)
        }
        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions
        return questions;
    }

    def getAbuseDiscussionQuestion(params,def siteId,int batchIndex){
        List questions = discussionBoardCacheService.getAbuseDiscussionQuestionCache(params,siteId,batchIndex)
        if(questions == null) questions = new ArrayList()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
        List sendQuestions = new ArrayList()
                questions.each { def q ->
                    def _id = q['_id'] // new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
                    User uQ = User.findByUsername(q['createdBy'])
                    q['userId'] = q['createdBy']
                    q['userName'] = uQ.name
                    q['uImgId'] = uQ.id
                    q['uImgName'] = uQ.profilepic
                    q['qImgId'] = _id
                    q['qImgName'] = q['img_name']
                    if(user != null && DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,user.username) != null){
                        q["voted"] = true
                    }
                    java.util.Date toDay = new java.util.Date()
                    long timeStampMillis = toDay.getTime();
                    DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
                    java.util.Date qDate = format.parse(q['dateCreated']);
                    if(timeStampMillis - qDate.getTime() <= 86400) {
                        double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                        if(hrs < 1) hrs = 1
                        q["hours"] = (int)hrs
                    }
//            if(batchIndex == 0) q['_id'].date = _id.date
            if(q.qaList!= null){
                List anslist = new ArrayList()
                q.qaList.each{def ans->
                    if(ans['showAnswer'] == true) anslist.add(ans)
                }
                q.qaList = anslist
                q.answerCount = anslist.size()
            }
                    sendQuestions.add(q)
        }
        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions
    }

    def getIncorrectDiscussionQuestion(params,def siteId,int batchIndex){
        List questions = discussionBoardCacheService.getIncorrectDiscussionQuestionCache(params,siteId,batchIndex)
        if(questions == null) questions = new ArrayList()
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
        List sendQuestions = new ArrayList()
                questions.each { def q ->
            def _id = q['_id'] //new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
                    User uQ = User.findByUsername(q['createdBy'])
                    q['userId'] = q['createdBy']
                    q['userName'] = uQ.name
                    q['uImgId'] = uQ.id
                    q['uImgName'] = uQ.profilepic
                    q['qImgId'] = _id
                    q['qImgName'] = q['img_name']
                    if(user != null && DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,user.username) != null){
                        q["voted"] = true
                    }
                    java.util.Date toDay = new java.util.Date()
                    long timeStampMillis = toDay.getTime();
                    DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
                    java.util.Date qDate = format.parse(q['dateCreated']);
                    if(timeStampMillis - qDate.getTime() <= 86400) {
                        double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                        if(hrs < 1) hrs = 1
                        q["hours"] = (int)hrs
                    }
            if(q.qaList!= null){
                List anslist = new ArrayList()
                q.qaList.each{def ans->
                    if(ans['showAnswer'] == true) anslist.add(ans)
                }
                q.qaList = anslist
                q.answerCount = anslist.size()
            }
            sendQuestions.add(q)
        }
        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions
    }

    def getMainPageQuestions(params,def siteId, int batchIndex, String filter){
        List questions = null
        if(filter.equals("all")) questions = discussionBoardCacheService.getRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
        else if(filter.equals("answered")) questions = discussionBoardCacheService.getRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        else if(filter.equals("unanswered")) questions = discussionBoardCacheService.getRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        if(questions == null) questions = new ArrayList()
        User user = null
        String userName = ""
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            userName = user.username
        }
//        DiscussionUsers discussionUser = null
//        if(user != null) discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qFallowing = null
//        if(discussionUser != null) qFallowing =  discussionUser.questionsFollowing
//        List qVotes = null
//        if(discussionUser != null) qVotes =  discussionUser.questionVoted
//        if(qFallowing == null) qFallowing = new ArrayList()
//        if(qVotes == null) qVotes = new ArrayList()

//        List qiscussionBoardUserPoints = DiscussionBoardUserPoints.findAllByUserId(user.username)
        List qVotes = new ArrayList()
        def qFallowing = null
//        for(DiscussionBoardUserPoints discussionBoardUserPoint: qiscussionBoardUserPoints){
//        }

        List sendQuestions = new ArrayList();
        questions.each { def q ->
            def _id = q['_id']//new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
            qFallowing = DiscussionQuestionsUserFollowing.findByQIdAndCreatedBy(_id,userName);
            User uQ = User.findByUsername(q['createdBy'])
            q['userId'] = q['createdBy']
            q['userName'] = uQ.name
            q['uImgId'] = uQ.id
            q['uImgName'] = uQ.profilepic
            q['qImgId'] = _id
            q['qImgName'] = q['img_name']
            java.util.Date toDay = new java.util.Date()
            long timeStampMillis = toDay.getTime();
            DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
            java.util.Date qDate = format.parse(q['dateCreated']);
//            Date qDate = q['dateCreated']
            if(timeStampMillis - qDate.getTime() <= 86400) {
                double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                if(hrs < 1) hrs = 1
                q["hours"] = (int)hrs
            }
            if(qFallowing != null) {
                    q["fallowing"] = true
                }
          if(DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,userName) != null){
                  q["voted"] = true
          }
//          if(batchIndex == 0) q['_id'].date = _id.date
            if(q.qaList!= null){
                q.qaList.sort { a, b ->
                a.upVote <=> b.upVote
            }
                List anslist = new ArrayList()
                q.qaList.each{def ans->
                    if(ans.answer != null && ans['show_answer'] == true) {
                        uQ = User.findByUsername(ans['created_by'])
                        ans['userId'] = ans['created_by']
                        ans['userName'] = uQ.name
                        ans['uImgId'] = uQ.id
                        ans['uImgName'] = uQ.profilepic
                        anslist.add(ans)
                    }
                }
                q.qaList = anslist
                q.answerCount = anslist.size()
            }
            sendQuestions.add(q)
        }

        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions;
    }

    def questionGlobalSearch(params,String keyWord,def siteId, String type,List tagsFilter,String paramFilter){
        List questions = new ArrayList<>()
        String batchKeyword=""
        List cacheQuestions = discussionBoardCacheService.getRecentAnsweredDiscussionQuestionCache(params,siteId,0)
        if(cacheQuestions == null) cacheQuestions = new ArrayList()
       if(type.equals("search")){
           cacheQuestions.each { q ->
                String question = q['question']
                String userName = q['userName']
               String tags = q['tags']
                if(question.toLowerCase().contains(keyWord)) {
                    if(paramFilter == null || paramFilter.isEmpty()) questions.add(question)
                   else if(tags.contains(paramFilter)) questions.add(question)
                }else if(userName != null && userName.toLowerCase().contains(keyWord)){
                    if(paramFilter == null || paramFilter.isEmpty()) questions.add(userName)
                    else if(tags.contains(paramFilter)) questions.add(userName)
//                    questions.add(userName)
                }
        }
       }else if(type.equals("questions")){
           cacheQuestions.each { q ->
               def _id = q['_id'] // new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
               String question = q['question']
               String userName = q['userName']
               String tags = q['tags']
               if(question.toLowerCase().replaceAll(System.lineSeparator(),"").contains(keyWord) || question.toLowerCase().replaceAll(System.lineSeparator(),"").equals(keyWord)) {
//                   q['_id'].date = _id.date
                   if(paramFilter == null || paramFilter.isEmpty()) questions.add(q)
                   else if(tags.contains(paramFilter)) questions.add(q)
               }else if(userName != null && userName.toLowerCase().contains(keyWord)){
//                   q['_id'].date = _id.date
                   if(paramFilter == null || paramFilter.isEmpty()) questions.add(q)
                   else if(tags.contains(paramFilter)) questions.add(q)
//                   questions.add(q) old comment
               }
           }
       }else if(type.equals("filter")){
           if((keyWord == null || keyWord.isEmpty() || keyWord.endsWith(" ")) && tagsFilter != null &&  tagsFilter.size() > 0){
               keyWord = tagsFilter.get(0)
           }
           if(params.batchId!=null && !"".equals(params.batchId)){
               batchKeyword=params.batchId+""
           }

           cacheQuestions.each { q ->
               DiscussionLevelDtl discussionLevelDtl
               String insQues=""
               def _id = q['_id']// new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
               String  tags = q['tags']
               Integer  dldId = q['dldId']
               if(dldId!=null){
                   discussionLevelDtl  =DiscussionLevelDtl.findById(new Long(dldId))
                  insQues =  discussionLevelDtl.batchId+""
               }
               if((insQues!="" && insQues.contains(batchKeyword)) && (keyWord == null || keyWord.isEmpty() || keyWord.endsWith(" "))){
                   questions.add(q)
               }else if(keyWord!=null && tags.equals(keyWord) && batchKeyword.isEmpty()){
                   questions.add(q)
               } else if((insQues!="" && insQues.contains(batchKeyword)) && (keyWord!=null && tags.equals(keyWord))){
                   questions.add(q)
               }

           }
       }
//        if(questions.size() < 10) {
//            BasicDBObject questionRegexQuery = new BasicDBObject();
//            if(!type.equals("filter")) questionRegexQuery.put("question", new BasicDBObject("\$regex", "/"+keyWord+".*").append("\$options", "si"));
//
//            BasicDBObject questionEqQuery = new BasicDBObject();
//            if(!type.equals("filter")) questionEqQuery.put("question", keyWord);
//
//            BasicDBObject tagRegexQuery = new BasicDBObject();
//            tagRegexQuery.put("tags", new BasicDBObject("\$all", tagsFilter));
//
//            BasicDBObject titleRegexQuery = new BasicDBObject();
//            if(!type.equals("filter")) titleRegexQuery.put("userName", new BasicDBObject("\$regex", keyWord+".*").append("\$options", "i"));
//
//            BasicDBList firstOrValues = new BasicDBList()
//            if(!type.equals("filter")) {
//                firstOrValues.add( questionRegexQuery )
//                firstOrValues.add( titleRegexQuery )
////                firstOrValues.add( questionEqQuery )
//            }
//            firstOrValues.add( tagRegexQuery )
//            DBObject firstOr = null
//            if(!type.equals("filter")) firstOr = new BasicDBObject( "\$or", firstOrValues )
//            else firstOr = tagRegexQuery
//            DBObject freeFilter = new BasicDBObject( "free", true )
//            DBObject siteIdFilter = new BasicDBObject( "siteId", siteId );
//            DBObject moderationFilter = new BasicDBObject( "show", true );
//            BasicDBList firstAndValues = new BasicDBList();
//            firstAndValues.add(freeFilter)
//            firstAndValues.add(siteIdFilter)
//            firstAndValues.add(moderationFilter)
//            if(paramFilter != null && ! paramFilter.isEmpty()) {
//                List paramFilterList = new ArrayList()
//                paramFilterList.add(paramFilter)
//                firstAndValues.add(new BasicDBObject("tags",new BasicDBObject("\$all",paramFilterList)))
//            }
//            DBObject firstAnd = new BasicDBObject( "\$and", firstAndValues )
//            BasicDBList finalAndValues = new BasicDBList()
//            finalAndValues.add(firstOr)
//            finalAndValues.add(firstAnd)
//            DBObject query = new BasicDBObject( "\$and", finalAndValues );
//            def db = mongo.getDB("wsdiscussion")
//            def dbObjects = db.getCollection("discussionQuestions").find(query).sort(["_id":-1])
//            if(type.equals("search")){
//                 dbObjects?.collect {
//                     String question = it['question']
//                     String userName = it['userName']
//                     if(question.toLowerCase().contains(keyWord) && questions.indexOf(question) == -1) {
//                         questions.add(question)
//                     }else if(userName != null && userName.toLowerCase().contains(keyWord) && questions.indexOf(userName) == -1){
//                         questions.add(userName)
//                     }
//                }
//            }else if(type.equals("questions") || type.equals("filter")){
//                dbObjects?.collect {
//                    Boolean match = false
//                    questions.each { q ->
//                        def _id = new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
//                        if(_id.equals(it['_id'])){
//                            match = true
//                        }
//                    }
//                    if(match == false) questions.add(it)
//                }
//            }
//        }
        User user = null
        List qiscussionBoardUserPoints
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
             qiscussionBoardUserPoints = DiscussionBoardUserPoints.findAllByUserId(user.username)
        }
        List qVotes = new ArrayList()
        List qFallowing = new ArrayList()
        List aVoted = new ArrayList()
        for(DiscussionBoardUserPoints discussionBoardUserPoint: qiscussionBoardUserPoints){
        }
        List sendQuestions = new ArrayList();
        if(!type.equals("search")){
                questions.each { def q ->
                def _id = q['_id']// new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
                int ansCount = 0
                    User uQ = User.findByUsername(q['createdBy'])
                    q['userId'] = q['createdBy']
                    q['userName'] = uQ.name
                    q['uImgId'] = uQ.id
                    q['uImgName'] = uQ.profilepic
                    q['qImgId'] = _id
                    q['qImgName'] = q['img_name']
                Instant instant = Instant.now();
                long timeStampMillis = instant.getEpochSecond();

                    DateFormat format = new SimpleDateFormat("MMMM d, yyyy hh:mm:ss aaa");
                    java.util.Date qDate = format.parse(q['dateCreated']);

                if(timeStampMillis - qDate.getTime() <= 86400) {
                    double hrs = (timeStampMillis - qDate.getTime())/(60 * 60)
                    if(hrs < 1) hrs = 1
                    q["hours"] = (int)hrs
                }
//                if(qFallowing != null && qFallowing.size() > 0){
//                    if(qFallowing.indexOf(_id) > -1) {
//                        q["fallowing"] = true
//                    }
//                }

                if(springSecurityService.currentUser != null && DiscussionQuestionsUserFollowing.findByQIdAndCreatedBy(_id,user.username) != null)      q["fallowing"] = true
//                if(qVotes != null && qVotes.size() > 0){
//                    if(qVotes.indexOf(_id) > -1) {
//                        q["voted"] = true
//                    }
//                }
                    if( springSecurityService.currentUser != null && DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,user.username) != null) q["voted"] = true
                if(q.qaList!= null){
                    q.qaList.each{ ans ->
                        if(ans['show_answer'] == true) {
                            uQ = User.findByUsername(ans['created_by'])
                            ans['userId'] = ans['created_by']
                            ans['userName'] = uQ.name
                            ans['uImgId'] = uQ.id
                            ans['uImgName'] = uQ.profilepic
                            ansCount = ansCount + 1
                        }
                    }
                    q.qaList.sort { a, b ->
                        a.upVote <=> b.upVote
                    }
                }
                q['answerCount'] = ansCount
                sendQuestions.add(q)
            }
        }else{
            sendQuestions.addAll(questions)
        }

        if(sendQuestions == null) sendQuestions = new ArrayList()
        return sendQuestions;
    }

    def getQuestionById(def request, String qId) {
        Boolean fallowing, voted
        int hours = 0
        String userName = ""
        def _id = 0//new ObjectId(request.JSON.timestamp, request.JSON.machineIdentifier, (short) request.JSON.processIdentifier, request.JSON.counter)
        if(qId != null) _id = Long.parseLong(qId)
        else _id = request.JSON.qId
        def discussionQuestion = DiscussionQuestions.findById(_id)
        User user = null
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            userName = user.username
        }
        def sendDiscussionQuestion = [
                                      '_id':discussionQuestion.id,
                                      'id':discussionQuestion.id,
                                        'question' : discussionQuestion.question,
                                      'free' : discussionQuestion.free,
                                      'tags' : discussionQuestion.tags,
                                      'showQuestion' : discussionQuestion.showQuestion,
                                      'siteId' : discussionQuestion.siteId,
                                      'ansCount' : discussionQuestion .ansCount,
                                      'upvoteCount' : discussionQuestion.upvoteCount,
                                      'imgName' : discussionQuestion.imgName,
                                      'dateCreated' : discussionQuestion.dateCreated,
                                      'createdBy':discussionQuestion.createdBy,
                                      'userId' : discussionQuestion.createdBy,
                                      'qImgId' : discussionQuestion.id,
                                      'qImgName' : discussionQuestion.imgName,
                                      'uImgId' : dataProviderService.getUserMst(discussionQuestion.createdBy).id,
                                      'uImgName' : dataProviderService.getUserMst(discussionQuestion.createdBy).profilepic,
                                      'userName' :dataProviderService.getUserMst(discussionQuestion.createdBy).name]
//        List discussionQuestionsList = dbObjects?.collect {
//            return it
//        }
//        def discussionQuestion = discussionQuestionsList.get(0)

//        DiscussionUsers discussionUser = null
//        if(user != null) discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qFallowing = null
//        if(discussionUser != null) qFallowing =  discussionUser.questionsFollowing
//        List qVotes = null
//        if(discussionUser != null) qVotes =  discussionUser.questionVoted
//        if(qFallowing == null) qFallowing = new ArrayList()
//        if(qVotes == null) qVotes = new ArrayList()

        List qVotes = new ArrayList()
        def qFallowing = null
        List aVoted = new ArrayList()

        java.util.Date toDay = new java.util.Date()
        long q_date = discussionQuestion.dateCreated.getTime(), timeStampMillis = toDay.getTime()
            if(timeStampMillis - q_date <= 86400) {
                double hrs = (timeStampMillis - q_date)/(60 * 60)
                if(hrs < 1) hrs = 1
                sendDiscussionQuestion['hours'] = (int)hrs
            }
//            if(qFallowing != null && qFallowing.size() > 0){
//                if(qFallowing.indexOf(_id) > -1) {
                    if(DiscussionQuestionsUserFollowing.findByQIdAndCreatedBy(_id,userName) != null) sendDiscussionQuestion['fallowing'] = true
//                }
//            }
        if(DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,userName) != null){
            sendDiscussionQuestion["voted"] = true
        }
        List answerList = DiscussionAnswers.findAllByDiscussionQuestionIdAndShowAnswer(discussionQuestion.id,true)
        if(user != null) {
            List answerListByUser = DiscussionAnswers.findAllByDiscussionQuestionIdAndCreatedByAndShowAnswerNotEqual(discussionQuestion.id,user.username,true)
            if(answerList != null && answerListByUser != null && answerListByUser.size() > 0 && answerList.size() > 0) {
                answerList.addAll(answerListByUser)
            }
            else if(answerListByUser != null && answerListByUser.size() > 0) {
                answerList = answerListByUser
            }
        }
        if(answerList == null) answerList = new ArrayList()
        List sendAnswerList = new ArrayList()
                answerList.each { ans ->
//            if((ans.answer != null && ans.showAnswer == true) || (user != null && ans.createdBy.equals(user.username))){
                def a = [
                        'id':ans.id,
                        'answer':ans.answer,
                        'discussionQuestionId':ans.discussionQuestionId,
                        'upVoteCount':ans.upVoteCount,
                        'imgName':ans.imgName,
                        'showAnswer':ans.showAnswer,
                        'dateCreated':ans.dateCreated,
                        'createdBy':ans.createdBy,
                         'userId':ans.createdBy,
                        'uImgId' : dataProviderService.getUserMst(ans.createdBy).id,
                        'uImgName' : dataProviderService.getUserMst(ans.createdBy).profilepic,
                        'userName':dataProviderService.getUserMst(ans['createdBy']).name
                ]
//                ans['userId'] = ans['createdBy']
//                ans['userName'] = dataProviderService.getUserMst(ans['createdBy']).name
                if(DiscussionBoardUserPoints.findByAnswerIdAndDiscussionPointsIdAndUserId(ans.id,discussionPointsMstIdForUpvotingAnswer,userName) != null) a['voted'] = true
                sendAnswerList.add(a)
//            }
        }
        sendDiscussionQuestion['qaList'] = sendAnswerList
        return ['discussionQuestion':sendDiscussionQuestion]
    }

    def fallowQuestion(def request){
        def _id = request.JSON.id
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        def discussionQuestionsUserFollowing = null
        if(user != null) discussionQuestionsUserFollowing = DiscussionQuestionsUserFollowing.findByCreatedBy(user.username)
        if(user != null && discussionQuestionsUserFollowing == null){
            discussionQuestionsUserFollowing = new DiscussionQuestionsUserFollowing()
            discussionQuestionsUserFollowing.qId = _id
            discussionQuestionsUserFollowing.createdBy = user.username
            discussionQuestionsUserFollowing.save(flush:true)
        }
        return discussionQuestionsUserFollowing
    }

    def upvoteQuestion(params,def request){
        def _id = request.JSON.id
        String siteIdStr = request.JSON.siteId
        Integer pointsForUpvotingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForUpvotingQuestion).getPointsValue()
        def discussionQuestion = DiscussionQuestions.findById(_id)

        if(discussionQuestion != null){
            discussionQuestion.upvoteCount = discussionQuestion.upvoteCount + 1
            User user = null
            if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            def discussionUsers = null
            if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
            if(discussionUsers != null){
                discussionUsers.setUserTotalPoints(discussionUsers.getUserTotalPoints() + pointsForUpvotingQuestion)
            }else {
                discussionUsers = new DiscussionBoardUser()
                discussionUsers.userId = user.username
                discussionUsers.userTotalPoints = pointsForUpvotingQuestion
            }
            DiscussionBoardUserPoints discussionBoardUserPoints = new DiscussionBoardUserPoints()
            discussionBoardUserPoints.userId = user.username
            discussionBoardUserPoints.discussionPointsId = discussionPointsMstIdForUpvotingQuestion
            discussionBoardUserPoints.questionId = _id
            discussionUsers.save(flush:true)
            discussionBoardUserPoints.save(flush:true)
            discussionQuestion.save(flush:true)
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params, Integer.parseInt(siteIdStr), 0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params, Integer.parseInt(siteIdStr), 0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params, Integer.parseInt(siteIdStr), 0)
        }else{
            return ['error':'Question is null']
        }
        return ['discussionQuestion':discussionQuestion]
    }

    def upvoteAnswer(params,def request){
        def _id = request.JSON.id
        String siteIdStr = request.JSON.siteId
        Integer pointsForUpvotingAnswer = DiscussionPointsMst.findById(discussionPointsMstIdForUpvotingAnswer).getPointsValue()
        def discussionAnswer = DiscussionAnswers.findById(_id)
        if(discussionAnswer != null){
            User user = null
            discussionAnswer.upVoteCount = discussionAnswer.upVoteCount + 1
            if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            def discussionUsers = null
            if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
            if(discussionUsers != null){
                discussionUsers.setUserTotalPoints(discussionUsers.getUserTotalPoints() + pointsForUpvotingAnswer)
            }else {
                discussionUsers = new DiscussionBoardUser()
                discussionUsers.userId = user.username
                discussionUsers.userTotalPoints = pointsForUpvotingAnswer
            }
            DiscussionBoardUserPoints discussionBoardUserPoints = new DiscussionBoardUserPoints()
            discussionBoardUserPoints.userId = user.username
            discussionBoardUserPoints.discussionPointsId = discussionPointsMstIdForUpvotingAnswer
            discussionBoardUserPoints.answerId = _id
            discussionUsers.save(flush:true)
            discussionBoardUserPoints.save(flush:true)
            discussionAnswer.save(flush:true)
            userPointsService.addPoints("answere upvoted "+ _id,pointsForUpvotingAnswer,1,discussionAnswer.createdBy)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            return ['discussionAnswer':discussionAnswer]
        }
//        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        def discussionUsers = null
//        if(user != null) discussionUsers = DiscussionUsers.findByUserid(user.username)
//        if(discussionUsers == null){
//            DiscussionUsers discussionNewUser = new DiscussionUsers()
//            List userQuestionIds = new ArrayList();
//            userQuestionIds.add(_id)
//            discussionNewUser.userid = user.username
//            discussionNewUser.userName = user.name
//            discussionNewUser.totalUpVoteCount = 0
//            discussionNewUser.questionIds = userQuestionIds
//            discussionNewUser.save(flush:true)
//        }
//
//        String userId = ""
//        if(user != null) userId = user.username
//        def discussionQuestion = DiscussionQuestions.findById(_id)
//        List discussionAnswers = discussionQuestion.qaList
//        for(def discussionAnswer:discussionAnswers){
//            long ansId = request.JSON.answerId
//            if(ansId == discussionAnswer.answerId){
//                userPointsService.addPoints("answere upvoted "+ ansId,2,1,discussionAnswer.userId)
//            }
//        }
//        if(discussionQuestion.archive == null){
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\$and:[ {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")},{" +
//                    "\"qaList.answerId\":" +
//                    request.JSON.answerId +
//                    "}]}, u: {  \$inc: { \"qaList.\$.upVote\": 1 }, \$push: { \"qaList.\$.uVoted\": \"" +
//                    user.username +
//                    "\" } }" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
//            if(user != null) discussionUsers =  DiscussionUsers.findByUserid(userId)
//            if(discussionUsers != null){
//                List userVotedAnswerQuestionuIds = null
//                if(discussionUsers.answerVoted == null ) userVotedAnswerQuestionuIds = new ArrayList()
//                else userVotedAnswerQuestionuIds = discussionUsers.answerVoted
//                userVotedAnswerQuestionuIds.add(request.JSON.answerId)
//                discussionUsers.answerVoted = userVotedAnswerQuestionuIds
//                discussionUsers.save(flush:true)
//            }else if(user != null) {
//                DiscussionUsers discussionNewUser = new DiscussionUsers()
//                List userVotedAnswerQuestionuIds = new ArrayList();
//                userVotedAnswerQuestionuIds.add(request.JSON.answerId)
//                discussionNewUser.userid = userId
//                discussionNewUser.userName = user.name
//                discussionNewUser.totalUpVoteCount = 0
//                discussionNewUser.answerVoted = userVotedAnswerQuestionuIds
//                discussionNewUser.save(flush:true)
//            }
//            String siteIdStr = request.JSON.siteId

            return ['discussionAnswer':null]
        }

    def archiveQuestoin(def request){
//        def _id = request.JSON.id
//        def database = mongo.getDatabase("wsdiscussion");
//        String comand ="{" +
//                "      update: \"discussionQuestions\"," +
//                "      updates: [" +
//                "         {" +
//                "           q: {\"_id\": ObjectId(\"" +
//                _id +
//                "\")}, u: {  \$set: { \"archive\": true } }" +
//                "         }" +
//                "      ]" +
//                "   }"
//        Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
        return "buildInfoResults"
    }

    def editQuestin(def request){
        def _id = request.JSON.id
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){
//           def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")}, u: {  \$set:{"
                    if(request.JSON.title != null) {
//                        comand = comand + " \"title\": \""+request.JSON.title+"\" "
                    }
                    if(request.JSON.question != null) {
//                        comand = comand + " , \"question\": \""+request.JSON.question+"\" "
                        discussionQuestion.question = request.JSON.question
                    }
                    if(request.JSON.tags != null){
                        String tags = request.JSON.tags
                        discussionQuestion.tags = tags
//                        List<String> tags = request.JSON.tags
//                        StringBuffer sb = new StringBuffer()
//                        sb.append("[")
//                        tags.each { tag ->
//                            if(tag.contains("subject_")){
//                                SubjectMst subjectMst = SubjectMst.findByName(tag.split("subject_")[1])
//                                if(subjectMst == null) {
//                                    subjectMst = new SubjectMst()
//                                    subjectMst.name = tag.split("subject_")[1]
//                                    subjectMst.syllabusType = "doubts"
//                                    subjectMst.country = "doubts"
//                                    subjectMst.save()
//                                }
//                            }
//                            sb.append("\"")
//                            sb.append(tag)
//                            sb.append("\",")
//                        }
//                        sb.append("]")
                        comand = comand + " , \"tags\": "+sb.toString()+" "
                    }
//                    comand = comand + "}}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
            discussionQuestion.save(flush:true)
            if(request.JSON.tags != null) {
                discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            }
            if(request.JSON.question != null) {
                discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            }
            return buildInfoResults
       }else {
            return ['error':'Question is archived. Question cannot be edited']
        }
    }

    def editQuestinWithImage(params,Long id, String question, int siteId,MultipartFile file,Boolean img){
        String qImgName = ""
        def _id = id//new ObjectId(timestamp, machineIdentifier,processIdentifier, counter)
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){

            if(img == true){

                File uploadDir = new File("upload/discussionDoubt/"+discussionQuestion.id)
                if(!uploadDir.exists()) uploadDir.mkdirs()

                //creating directory to process images
                File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
                if(!uploadDir1.exists()) uploadDir1.mkdirs()
                String filename=file.originalFilename
                filename=filename.replaceAll("\\s+","")
                BufferedImage image = ImageIO.read(file.getInputStream())

                ByteArrayOutputStream baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte1 = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte2 = baos.toByteArray()
                baos.close()

                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

                //saving original image finally
                file.transferTo(new File(uploadDir.absolutePath+"/"+file.filename))
                qImgName = file.filename

            }


//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")}, u: {  \$set:{"
            if(img == true) {
                discussionQuestion.imgName = qImgName
//                comand = comand + " \"qImgName\": \""+qImgName+"\" "
            }
            if(question != null) {
//                comand = comand + " , \"question\": \""+question+"\" "
                discussionQuestion.question = question
            }
//            comand = comand + "}}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
            discussionQuestion.save(flush:true)
            def json = null
            def removeAbuse = removeAbuse(_id,siteId+"")
            def removeIncorrect = removeIncorrect(_id,siteId+"")
            discussionBoardCacheService.updateAbuseDiscussionQuestionCache(siteId,0)
            discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(siteId,0)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(siteId)
            discussionBoardCacheService.updateDiscussionAbuseAnswersCache(siteId)
            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(siteId)
            json=['questionedited':discussionQuestion,'removeIncorrect':removeIncorrect,'removeAbuse':removeAbuse]
            return json
        }else {
            return ['error':'Question is archived. Question cannot be edited']
        }
    }

    def editAnswer(def request){
        def _id = request.JSON.answerId
        def answers = DiscussionAnswers.findById(_id)
        if(true){
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\$and:[ {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")},{" +
//                    "\"qaList.answerId\":" +
//                    request.JSON.answerId +
//                    "}]}," +
//                    " u: {  \$set: { \"qaList.\$.answer\": \"" +
//                    request.JSON.answer +
//                    "\" } }" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
            answers.answer = request.JSON.answer
            answers.save(flush:true)
            return answers
        }else {
            return ['error':'Question is archived. Answer cannot be edited']
        }
    }

    def deleteQuestion(params,def request,def siteName, def clientName,def siteId){
        try{
            long _id = request.JSON.id
            DiscussionQuestions questions = DiscussionQuestions.findById(_id)
            def user = User.findByUsername(questions.createdBy)
            questions.delete(flush:true)
            DiscussionAnswers.findAllByDiscussionQuestionId(_id).each { it.delete(flush:true) }
            List qPropList = DiscussionQuestionsProperties.findAllByDiscussionQuestionId(_id)
            List dUserFlwing = DiscussionQuestionsUserFollowing.findAllByQId(_id)
            for(DiscussionQuestionsProperties discussionQuestionsProperties:qPropList){
                discussionQuestionsProperties.delete(flush:true)
            }
            for(DiscussionQuestionsUserFollowing discussionQuestionsUserFollowing:dUserFlwing){
                discussionQuestionsUserFollowing.delete(flush:true)
            }
            Integer pointsForAddingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForAddingQuestion).getPointsValue()
            def discussionUsers =  DiscussionBoardUser.findByUserId(questions.createdBy)

            List discussionBoardUserPoints = DiscussionBoardUserPoints.findAllByQuestionId(_id)
            for(DiscussionBoardUserPoints discussionBoardUserPoint:discussionBoardUserPoints){
                discussionBoardUserPoint.delete(flush:true)
            }
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingQuestion
            discussionUsers.save(flush:true)
            discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)

            discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            //discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
            sendEmail(user.email,user.name, "Your Question is Deleted","Question Deleted", siteId, siteName,clientName, "")
            return questions
        }catch(Exception ex){
            return 1
        }
    }



    def deleteQuestionByinstituteAdmin(params,siteId){
        try{
            long _id = new Long(params.id)
            DiscussionQuestions questions = DiscussionQuestions.findByIdAndDldIdIsNotNull(_id)
            def user = User.findByUsername(questions.createdBy)
            questions.delete(flush:true)
            DiscussionAnswers.findAllByDiscussionQuestionId(_id).each { it.delete(flush:true) }
            List qPropList = DiscussionQuestionsProperties.findAllByDiscussionQuestionId(_id)
            List dUserFlwing = DiscussionQuestionsUserFollowing.findAllByQId(_id)
            for(DiscussionQuestionsProperties discussionQuestionsProperties:qPropList){
                discussionQuestionsProperties.delete(flush:true)
            }
            for(DiscussionQuestionsUserFollowing discussionQuestionsUserFollowing:dUserFlwing){
                discussionQuestionsUserFollowing.delete(flush:true)
            }
            Integer pointsForAddingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForAddingQuestion).getPointsValue()
            def discussionUsers =  DiscussionBoardUser.findByUserId(questions.createdBy)

            List discussionBoardUserPoints = DiscussionBoardUserPoints.findAllByQuestionId(_id)
            for(DiscussionBoardUserPoints discussionBoardUserPoint:discussionBoardUserPoints){
                discussionBoardUserPoint.delete(flush:true)
            }
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingQuestion
            discussionUsers.save(flush:true)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,0)
            return questions
        }catch(Exception ex){
            println("prints"+ex.stackTrace)
        }
    }
    def deleteAnswer(params,def request,def clientName, def siteName,def siteId){
        try{
            String usrName = ""
            def _id = request.JSON.answerId
            DiscussionAnswers answers = DiscussionAnswers.findById(_id)
            User user = dataProviderService.getUserMst(answers.createdBy)
            List discussinAnswerProperties = DiscussionAnswersProperties.findAllByDiscussionAnswerId(_id)
            List discussionUserPoints = DiscussionBoardUserPoints.findAllByAnswerId(_id)
            Integer pointsForAddingAnswer = DiscussionPointsMst.findById(discussionPointsMstIdForAddingAnswer).getPointsValue()
            def discussionUsers =  DiscussionBoardUser.findByUserId(answers.createdBy)
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingAnswer
            discussionUsers.save(flush:true)
            for(DiscussionAnswersProperties answersProperties:discussinAnswerProperties){
                answersProperties.delete(flush:true)
            }
            for(DiscussionBoardUserPoints discussionBoardUserPoints:discussionUserPoints){
                discussionBoardUserPoints.delete(flush:true)
            }
            answers.delete(flush:true)

            discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId));
            discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            if(user != null && user.email != null) sendEmail(user.email,user.name, "Your Answer is Deleted","Answer Deleted", siteId, siteName,clientName, "")
            return 0
        }catch(Exception ex){
            return 1
        }
    }

    def addAbuse(params,def request){
        def _id = request.JSON.id
        def discussionQuestion = DiscussionQuestions.findById(_id) //discussionQuestion.archive == null
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        discussionQuestion.showQuestion = false
        def discussionUsers = null
        if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
        if(discussionUsers == null){
            discussionUsers = new DiscussionBoardUser()
            discussionUsers.userId = user.username
            discussionUsers.userTotalPoints = 0
        }
        DiscussionQuestionsProperties discussionQuestionsProperties = new DiscussionQuestionsProperties()
        discussionQuestionsProperties.discussionQuestionId = _id
        discussionQuestionsProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForAbuse
        if(user != null) discussionQuestionsProperties.createdBy = user.username
        discussionQuestionsProperties.save(flush:true)
        discussionQuestion.save(flush:true)
        discussionBoardCacheService.updateAbuseDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        return ['discussionQuestionsProperties':discussionQuestionsProperties,'discussionQuestion':discussionQuestion]
    }

    def addIncorrect(params,def request){
        def _id = request.JSON.id
        def discussionQuestion = DiscussionQuestions.findById(_id) //discussionQuestion.archive == null
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        discussionQuestion.showQuestion = false
        def discussionUsers = null
        if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
        if(discussionUsers == null){
            discussionUsers = new DiscussionBoardUser()
            discussionUsers.userId = user.username
            discussionUsers.userTotalPoints = 0
        }
        DiscussionQuestionsProperties discussionQuestionsProperties = new DiscussionQuestionsProperties()
        discussionQuestionsProperties.discussionQuestionId = _id
        discussionQuestionsProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForIncorrect
        if(user != null) discussionQuestionsProperties.createdBy = user.username
        discussionQuestionsProperties.save(flush:true)
        discussionQuestion.save(flush:true)
//        discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        return ['discussionQuestionsProperties':discussionQuestionsProperties,'discussionQuestion':discussionQuestion]
    }

    def addAnswerAbuse(params,def request){


        def _id = request.JSON.id
        def discussionAnswer = DiscussionAnswers.findById(_id) //discussionQuestion.archive == null
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        discussionAnswer.showAnswer = false
        def discussionUsers = null
        if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
        if(discussionUsers == null){
            discussionUsers = new DiscussionBoardUser()
            discussionUsers.userId = user.username
            discussionUsers.userTotalPoints = 0
        }
        DiscussionAnswersProperties discussionAnswerProperties = new DiscussionAnswersProperties()
        discussionAnswerProperties.discussionAnswerId = _id
        discussionAnswerProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForAbuse
        discussionAnswerProperties.createdBy = user.username
        discussionAnswerProperties.save(flush:true)
        discussionAnswer.save(flush:true)
//        discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
        discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        return ['discussionAnswerProperties':discussionAnswerProperties,'discussionAnswer':discussionAnswer]


//        def discussionQuestion = DiscussionQuestions.findById(_id)
//
//        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        def discussionUsers = null
//        if(user != null) discussionUsers = DiscussionUsers.findByUserid(user.username)
//        if(discussionUsers == null){
//            DiscussionUsers discussionNewUser = new DiscussionUsers()
//            List userQuestionIds = new ArrayList();
//            userQuestionIds.add(_id)
//            discussionNewUser.userid = user.username
//            discussionNewUser.userName = user.name
//            discussionNewUser.totalUpVoteCount = 0
//            discussionNewUser.questionIds = userQuestionIds
//            discussionNewUser.save(flush:true)
//        }
//
//        if(discussionQuestion.archive == null){
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\$and:[ {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")},{" +
//                    "\"qaList.answerId\":" +
//                    request.JSON.answerId +
//                    "}]}," +
//                    " u: {  \$set: { \"qaList.\$.abuse\": 0" +
//                    " } }" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
    }

    def addAnswerIncorrect(params,def request){
        def _id = request.JSON.id
        def discussionAnswer = DiscussionAnswers.findById(_id) //discussionQuestion.archive == null
        User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        discussionAnswer.showAnswer = false
        def discussionUsers = null
        if(user != null ) discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
        if(discussionUsers == null){
            discussionUsers = new DiscussionBoardUser()
            discussionUsers.userId = user.username
            discussionUsers.userTotalPoints = 0
        }
        DiscussionAnswersProperties discussionAnswerProperties = new DiscussionAnswersProperties()
        discussionAnswerProperties.discussionAnswerId = _id
        discussionAnswerProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForIncorrect
        discussionAnswerProperties.createdBy = user.username
        discussionAnswerProperties.save(flush:true)
        discussionAnswer.save(flush:true)
//        discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
        discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        return ['discussionAnswerProperties':discussionAnswerProperties,'discussionAnswer':discussionAnswer]
//        def _id = request.JSON.id
//
//        User user = null
//        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
//        def discussionUsers = null
//        if(user != null) discussionUsers = DiscussionUsers.findByUserid(user.username)
//        if(discussionUsers == null){
//            DiscussionUsers discussionNewUser = new DiscussionUsers()
//            List userQuestionIds = new ArrayList();
//            userQuestionIds.add(_id)
//            discussionNewUser.userid = user.username
//            discussionNewUser.userName = user.name
//            discussionNewUser.totalUpVoteCount = 0
//            discussionNewUser.questionIds = userQuestionIds
//            discussionNewUser.save(flush:true)
//        }
//
//        def discussionQuestion = DiscussionQuestions.findById(_id)
//        if(discussionQuestion != null && discussionQuestion.archive == null){
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\$and:[ {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")},{" +
//                    "\"qaList.answerId\":" +
//                    request.JSON.answerId +
//                    "}]}," +
//                    " u: {  \$set: { \"qaList.\$.incorrect\": 0" +
//                    " } }" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
//            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
//            return buildInfoResults
//        }else {
//            return ['error':'Question is archived. Question cannot be raised as incorrect']
//        }
    }

    def confirmAnswerAbuse(def request){
//        def _id = request.JSON.id
//        def discussionQuestion = DiscussionQuestions.findById(_id)
//        if(discussionQuestion.archive == null){
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\$and:[ {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")},{" +
//                    "\"qaList.answerId\":" +
//                    request.JSON.answerId +
//                    "}]}," +
//                    " u: {  \$set: { \"qaList.\$.abuse\": 1" +
//                    " } }" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
//
//            comand ="{" +
//                    "      update: \"discussionUsers\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"answeredIds\": " +
//                    request.JSON.answerId +
//                    "}, u: {  \$inc:{\"score\": -10 }}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults1 = database.runCommand(new Document(Document.parse(comand)));
//
//            return ['questionsEdited':buildInfoResults,'userAnswerScoreEdited':buildInfoResults1]
//        }else {
//            return ['error':'Question is archived. Answer cannot be confirmed as abuse']
//        }
    }

    def removeAnswerAbuse(def request){
        def _id = request.JSON.answerId
        DiscussionAnswers answers = DiscussionAnswers.findById(_id)
        answers.showAnswer = true
        answers.save(flush:true)
        def discussionAnswerProperties = DiscussionAnswersProperties.findByDiscussionAnswerIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForAbuse)
        if(true){
            discussionAnswerProperties.delete(flush:true)
            discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
            return ['message':'Answer is no longer marked abuse']
        }else {
            return ['error':'Question is archived. Answer cannot be confirmed as not abuse']
        }
    }

    def confirmAnswerIncorrect(def request){
        def _id = request.JSON.id
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){

            Integer pointsForAddingAnswer = DiscussionPointsMst.findById(discussionPointsMstIdForAddingAnswer).getPointsValue()
            DiscussionAnswersProperties discussionAnswersProperties = DiscussionAnswersProperties.findByDiscussionAnswerIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForIncorrect)
            discussionAnswersProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForConfirmIncorrect
            def discussionUsers =  DiscussionBoardUser.findByUserId(discussionQuestion.createdBy)
            DiscussionBoardUserPoints discussionBoardUserPoints = DiscussionBoardUserPoints.findByUserIdAndAnswerIdAndDiscussionPointsId(discussionUsers.userId,_id,discussionPropertiesMstIdForAbuse)
            discussionBoardUserPoints.delete(flush:true)
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingAnswer
            discussionUsers.save(flush:true)
            discussionAnswersProperties.save(flush:true)

            discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            return ['questionsEdited':discussionQuestion,'userQuestionScoreEdited':discussionUsers]


        }else {
            return ['error':'Question is archived. Answer cannot be confirmed as incorrect']
        }
    }

    def removeAnswerIncorrect(def request){
        def _id = request.JSON.answerId
        DiscussionAnswers answers = DiscussionAnswers.findById(_id)
        answers.showAnswer = true
        answers.save(flush:true)
        def discussionAnswerProperties = DiscussionAnswersProperties.findByDiscussionAnswerIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForIncorrect)
        if(true){
            discussionAnswerProperties.delete(flush:true)
            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
            return ['message':'Answer is no longer marked incorrect']
        }else {
            return ['error':'Question is archived. Answer cannot be confirmed as not incorrect']
        }
    }

    def moderateAnswer(params,def request,def clientName,def siteName,def serverUrl){
        User user = null
//        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)

        def _id = request.JSON.answerId
        def qId = request.JSON.id
        def discussionQuestion = DiscussionQuestions.findById(qId)
        def discussionAnswer = DiscussionAnswers.findById(_id)
        if(true){
            discussionAnswer.showAnswer = true
            discussionAnswer.save(flush:true)
            String questionLink = serverUrl + "/discussion/discussionBoard?qId="+request.JSON.id+"&open=questionExplorer"
            int count = 0;
//            for(int i=0; i<discussionQuestion.qaList.size();i++){
//                long ansId = request.JSON.answerId
//                if(discussionQuestion.qaList.get(i).answerId == ansId) {
                    user = User.findByUsername(discussionQuestion.createdBy)
                    if(user!= null && user.email != null && !user.email.isEmpty() && DiscussionAnswers.findAllByDiscussionQuestionId(qId).size()<3) sendEmail(user.email,user.name, "Your Question has been Answered.","Question Answered", Integer.parseInt(request.JSON.siteId), siteName,clientName, questionLink)
                    user = User.findByUsername(discussionAnswer.createdBy)
                    if(user != null && user.email != null && !user.email.isEmpty()) sendEmail(user.email,user.name, "Your Answer is Approved.","Answer Approved", Integer.parseInt(request.JSON.siteId), siteName,clientName, questionLink)
//                }
//                if(discussionQuestion.qaList.get(i).show) count ++;
//            }


            discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            userPointsService.addPoints("answered "+ request.JSON.answerId,5,1,user.username)
            userPointsService.addPoints("doubt "+ discussionQuestion.id +" answered",5,1,discussionQuestion.createdBy)
            return discussionAnswer
        }else {
            return ['error':'Question is archived. Answer cannot be confirmed as not incorrect']
        }
    }

    def confirmAbuse(def id, def siteId){
        def _id = id//new ObjectId(Integer.parseInt(timestamp), Integer.parseInt(machineIdentifier), (short) processIdentifier, Integer.parseInt(counter))
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){
            Integer pointsForAddingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForAddingQuestion).getPointsValue()
            DiscussionQuestionsProperties discussionQuestionsProperties = DiscussionQuestionsProperties.findByDiscussionQuestionIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForAbuse)
            discussionQuestionsProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForConfirmAbus
            def discussionUsers =  DiscussionBoardUser.findByUserId(discussionQuestion.createdBy)
            DiscussionBoardUserPoints discussionBoardUserPoints = DiscussionBoardUserPoints.findByUserIdAndQuestionIdAndDiscussionPointsId(discussionUsers.userId,_id,discussionPropertiesMstIdForAbuse)
            discussionBoardUserPoints.delete(flush:true)
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingQuestion
            discussionUsers.save(flush:true)
            discussionQuestionsProperties.save(flush:true)

            discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(siteId),0)
            return ['questionsEdited':discussionQuestion,'userQuestionScoreEdited':discussionUsers]
       }else {
            return ['error':'Question is archived. Question cannot be confirmed as abuse']
        }
    }

    def removeAbuse(params,def id, def siteId){
        def _id = id //new ObjectId(timestamp, machineIdentifier, (short) processIdentifier, counter)
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){
            discussionQuestion.showQuestion = true;
            discussionQuestion.save(flush:true)
            DiscussionQuestionsProperties discussionQuestionsProperties = DiscussionQuestionsProperties.findByDiscussionQuestionIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForAbuse)
            discussionQuestionsProperties.delete(flush:true)
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")}, u: {  \$unset:{\"abuse\":''}}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
            discussionBoardCacheService.updateAbuseDiscussionQuestionCache(params,Integer.parseInt(siteId),0)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(siteId),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteId),0)
            return 0
        }else {
            return ['error':'Question is archived. Question cannot be confirmed as not abuse']
        }
    }

    def confirmIncorrect(def request){
        def _id = request.JSON.id
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){
            Integer pointsForAddingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForAddingQuestion).getPointsValue()
            DiscussionQuestionsProperties discussionQuestionsProperties = DiscussionQuestionsProperties.findByDiscussionQuestionIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForIncorrect)
            discussionQuestionsProperties.discussionBoardPropertiesId = discussionPropertiesMstIdForConfirmIncorrect
            def discussionUsers =  DiscussionBoardUser.findByUserId(discussionQuestion.createdBy)
            DiscussionBoardUserPoints discussionBoardUserPoints = DiscussionBoardUserPoints.findByUserIdAndQuestionIdAndDiscussionPointsId(discussionUsers.userId,_id,discussionPropertiesMstIdForIncorrect)
            discussionBoardUserPoints.delete(flush:true)
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingQuestion
            discussionUsers.save(flush:true)
            discussionQuestionsProperties.save(flush:true)

            discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            return ['questionsEdited':discussionQuestion,'userQuestionScoreEdited':discussionUsers]
        }else {
            return ['error':'Question is archived. Question cannot be confirmed as incorrect']
        }
    }

    def removeIncorrect(params,Long id, def siteId){
        def _id = id//new ObjectId(timestamp, machineIdentifier, (short) processIdentifier, counter)
        def discussionQuestion = DiscussionQuestions.findById(_id)
        if(true){
            discussionQuestion.showQuestion = true;
            discussionQuestion.save(flush:true)
            DiscussionQuestionsProperties discussionQuestionsProperties = DiscussionQuestionsProperties.findByDiscussionQuestionIdAndDiscussionBoardPropertiesId(_id,discussionPropertiesMstIdForIncorrect)
            discussionQuestionsProperties.delete(flush:true)
            discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(params,Integer.parseInt(siteId),0)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(siteId),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteId),0)
            return ['message':'Question is no longer marked as incorrect']
        }else {
            return ['error':'Question is archived. Question cannot be confirmed as not incorrect']
        }
    }

    def getFallowingDoubts(params,int batchIndex){
        String dldIds=""
        if(params.instituteId!=null && !"".equals(params.instituteId)){
            if(params.instituteId!=null && !"".equals(params.instituteId)) {
                String sql ="SELECT group_concat(id) FROM wsuser.discussion_level_dtl where institute_id="+params.instituteId
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                dldIds= results[0][0]
            }
        }
        User user = null
        String userName =""
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        userName = user.username
        String sql = " SELECT dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dq.upvote_count" +
                " FROM discussion_questions dq" +
//                " ON dq.id = dquf.q_id" +
                " WHERE 1 =1 " ;
        if(dldIds!=""){
            sql +=        " AND dq.dld_id IN ("+dldIds+")"
        }else{
            sql +=   " AND dq.dld_id is null";
        }
        sql +=        " AND dq.created_by = '" + userName + "'"+
                " UNION " +
                " SELECT dq1.id _id," +
                " dq1.question," +
                " dq1.tags," +
                " dq1.created_by createdBy," +
                " dq1.img_name," +
                " dq1.date_created dateCreated," +
                " dq1.upvote_count" +
                " FROM discussion_questions dq1 INNER JOIN discussion_questions_user_following dquf" +
                " ON dq1.id = dquf.q_id" ;
                " WHERE 1 =1 " ;

        if(dldIds!=""){
            sql +=        " AND dq1.dld_id IN ("+dldIds+")"
        }else{
            sql +=   " AND dq1.dld_id is null";
        }
        sql +=   " AND dq1.created_by != '" + userName + "'"+
                " ORDER BY dateCreated DESC"
//                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)
        print("sql"+sql)
        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            questions.add(r)
        }

        List sendQuestions = new ArrayList();
        User uQ = null
                questions.each { def q ->
            def _id = q['_id']//new ObjectId(q['_id'].timestamp, q['_id'].machineIdentifier, (short) q['_id'].processIdentifier, q['_id'].counter)
            uQ = User.findByUsername(q['createdBy'])
            q['userId'] = q['createdBy']
            q['userName'] = uQ.name
            q['uImgId'] = uQ.id
            q['uImgName'] = uQ.profilepic
            q['qImgId'] = q['_id']
            q['qImgName'] = q['img_name']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                uQ = User.findByUsername(a['created_by'])
                a['userId'] = a['created_by']
                a['userName'] = uQ.name
                a['uImgId'] = uQ.id
                a['uImgName'] = uQ.profilepic
                ansqwersOfAQuestion.add(a)
            }
            q['qaList'] = ansqwersOfAQuestion
            q['answerCount'] = ansqwersOfAQuestion.size()
            java.util.Date toDay = new java.util.Date()
            long timeStampMillis = toDay.getTime();
            Timestamp q_date = q['dateCreated']
            if(timeStampMillis - q_date.getTime() <= 86400) {
                double hrs = (timeStampMillis - q_date.getTime())/(60 * 60)
                if(hrs < 1) hrs = 1
                q["hours"] = (int)hrs
            }
            if(DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,userName) != null){
                q["voted"] = true
            }
            sendQuestions.add(q)
        }
        long count = sendQuestions.size()

        return ['questions':sendQuestions,'count':count]
    }

    def getMyAnswers(params,int batchIndex){
        User user = null
        String userName = ""
        int count = 0
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        userName = user.username
        List qiscussionBoardUserPoints = DiscussionBoardUserPoints.findAllByUserId(user.username)
        List qVotes = new ArrayList()
        List qFallowing = new ArrayList()
//        for(DiscussionBoardUserPoints discussionBoardUserPoint: qiscussionBoardUserPoints){
//        }
        String dldIds=""
        if(params.instituteId!=null && !"".equals(params.instituteId)){
            if(params.instituteId!=null && !"".equals(params.instituteId)) {
                String sql ="SELECT group_concat(id) FROM wsuser.discussion_level_dtl where institute_id="+params.instituteId
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                dldIds= results[0][0]
            }
        }
        Integer skip = batchIndex * 100
        String sql = " SELECT distinct dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dq.upvote_count" ;

        sql +=    " FROM discussion_questions dq,discussion_answers a" +
                " WHERE dq.id = a.discussion_question_id" ;
              if(dldIds!=""){
                sql +=        " AND dq.dld_id IN  ("+dldIds+")"
           }else{
            sql +=        " AND dq.dld_id is null"
           }
        sql +=   " AND a.created_by = '" + user.username + "'" +
                " LIMIT 100 OFFSET " + skip
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List questions = new ArrayList()

//        List questions = null
//        Bson userIdFilter = Filters.eq("qaList.userId",user.username)
//        long count = DiscussionQuestions.collection.count(userIdFilter)
//        def outputResults = DiscussionQuestions.collection.aggregate(Arrays.asList(
//                Aggregates.unwind("\$"+"qaList"),
//                Aggregates.match(userIdFilter),
//                Aggregates.group("\$"+"_id",Accumulators.push("answersList","\$"+"\$ROOT")),
//                Aggregates.skip(batchIndex * 100),
//                Aggregates.limit(100),
//                Aggregates.project(
//                        Projections.fields(
//                                Projections.include("answersList.question"),
//                                Projections.include("answersList.title"),
//                                Projections.include("answersList.tags"),
//                                Projections.include("answersList.qaList.answer"),
//                                Projections.include("answersList.qaList.answerId"),
//                                Projections.include("answersList.qaList.createdOn"),
//                                Projections.include("answersList.qaList.imgName"),
//                                Projections.include("answersList.qaList.upVote"),
//                                Projections.include("answersList.qImgId"),
//                                Projections.include("answersList.qImgName"),
//                                Projections.include("answersList.uImgId"),
//                                Projections.include("answersList.uImgName"),
//                                Projections.include("answersList.userName"),
//                                Projections.include("answersList.userId"),
//                        )
//                )
//        ))
//        questions = new ArrayList()
//        DiscussionUsers discussionUser = DiscussionUsers.findByUserid(user.username)
//        List qVotes = new ArrayList()
//        if(discussionUser != null) qVotes = discussionUser.questionVoted
//        List qFallowing = new ArrayList()
//        if(discussionUser != null) qFallowing = discussionUser.questionsFollowing
        User uQ = null
        results.collect { r ->
            def _id = r['_id']
            uQ = User.findByUsername(r['createdBy'])
            r['userId'] = r['createdBy']
            r['userName'] = uQ.name
            r['uImgId'] = uQ.id
            r['uImgName'] = uQ.profilepic
            r['qImgId'] = r['_id']
            r['qImgName'] = r['img_name']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                uQ = User.findByUsername(a['created_by'])
                a['userId'] = a['created_by']
                a['userName'] = uQ.name
                a['uImgId'] = uQ.id
                a['uImgName'] = uQ.profilepic
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            r['userId'] = r['createdBy']
            java.util.Date today = new java.util.Date()
            long timeStampMillis = today.getTime();
            Timestamp q_date = r['dateCreated']
            if(timeStampMillis - q_date.getTime() <= 86400) {
                double hrs = (timeStampMillis - q_date.getTime())/(60 * 60)
                if(hrs < 1) hrs = 1
                r["hours"] = (int)hrs
            }
                if(DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,userName) != null){
                    r["voted"] = true
                }
                if(DiscussionQuestionsUserFollowing.findByQIdAndCreatedBy(_id,userName) != null) {
                    r["fallowing"] = true
                }
            questions.add(r)
        }

        return ['questions':questions,'count':count]
    }

    def downVote(params,def request){
        try{
            def _id = request.JSON.id
            def discussionQuestion = DiscussionQuestions.findById(_id)
            User user = null
            def discussionUsers = null
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if(user != null ) {
                Integer pointsForUpvotingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForUpvotingQuestion).getPointsValue()
                discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
                String userName = user.username
                def discussionBoardUserPoints = DiscussionBoardUserPoints.findByQuestionIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingQuestion,userName)
                discussionBoardUserPoints.delete(flush:true)
                discussionUsers.setUserTotalPoints(discussionUsers.getUserTotalPoints() - pointsForUpvotingQuestion)
                discussionQuestion.upvoteCount = discussionQuestion.upvoteCount - 1
                discussionUsers.save(flush:true)
                discussionQuestion.save(flush:true)
            }
        }

            String siteIdStr = request.JSON.siteId
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            return [discussionUsers:discussionUsers,discussionQuestion: discussionQuestion]
        }catch(Exception ex){

            return 1
        }
    }

    def downVoteAnswer(params,def request){
        try{
            long _id = Long.parseLong(request.JSON.id)
            User user = null
        if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            def discussionAnswer = DiscussionAnswers.findById(_id)
            def discussionUsers = null
            if(springSecurityService.currentUser != null) {
                user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                if(user != null ) {
                    Integer pointsForUpvotingAnswer = DiscussionPointsMst.findById(discussionPointsMstIdForUpvotingAnswer).getPointsValue()
                    discussionUsers =  DiscussionBoardUser.findByUserId(user.username)
                    String userName = user.username
                    def discussionBoardUserPoints = DiscussionBoardUserPoints.findByAnswerIdAndDiscussionPointsIdAndUserId(_id,discussionPointsMstIdForUpvotingAnswer,userName)
                    discussionBoardUserPoints.delete(flush:true)
                    discussionUsers.setUserTotalPoints(discussionUsers.getUserTotalPoints() - pointsForUpvotingAnswer)
                    discussionAnswer.upVoteCount = discussionAnswer.upVoteCount - 1
                    discussionUsers.save()
                    discussionAnswer.save()
                    userPointsService.addPoints("answere upvoted "+ _id,-pointsForUpvotingAnswer,1,discussionAnswer.createdBy)
                }
            }
//
//
//
//
//            def database = mongo.getDatabase("wsdiscussion");
//            String comand =""
//            Document buildInfoResults = null
//            if(user!=null) {
//                comand = "{" +
//                    "      update: \"discussionUsers\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {userid:\"" +
//                    user.username +
//                    "\"},u:{\$pull:{answerVoted:" +
//                    request.JSON.answerId +
//                    "}}," +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//                buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
//            }
//            comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\$and:[ {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")},{" +
//                    "\"qaList.answerId\":" +
//                    request.JSON.answerId +
//                    "}]}, u: {  \$inc: { \"qaList.\$.upVote\": -1 }";
//                    if(user!=null)comand = comand + ",\$pull:{\"qaList.\$.uVoted\":\"" +
//                    user.username +
//                    "\"}";
//                    comand = comand +" }" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoAnswerResults = database.runCommand(new Document(Document.parse(comand)));
            String siteIdStr = request.JSON.siteId
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(siteIdStr),0)
//            List discussionAnswers = discussionQuestion.qaList
//            for(def discussionAnswer:discussionAnswers){
//                long ansId = request.JSON.answerId
//                if(ansId == discussionAnswer.answerId){
//                }
//            }
            return [discussionAnswer:discussionAnswer]
        }catch(Exception ex){

            return 1
        }
    }

    def unFallow(def request){
        try{
            def _id = request.JSON.id
            User user = null

            if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            def discussionQuestionsUserFollowing = null
            if(user != null) discussionQuestionsUserFollowing = DiscussionQuestionsUserFollowing.findByCreatedBy(user.username)
            if(user != null && discussionQuestionsUserFollowing != null){
                discussionQuestionsUserFollowing.delete(flush:true)
            }
            return discussionQuestionsUserFollowing
        }catch(Exception ex){

            return 1
        }
    }

    def deleteMultipleQuestions(params,def request){
        List<Long> ids = request.JSON.ids
        if(ids == null) ids = new ArrayList()
        List<DiscussionQuestions> questions = DiscussionQuestions.findAllByIdInList(ids)
        DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids).each { it.delete(flush:true) }
        for(DiscussionQuestions q:questions) {
            q.delete(flush:true)
            Long _id = q.id
            List qPropList = DiscussionQuestionsProperties.findAllByDiscussionQuestionId(_id)
            List dUserFlwing = DiscussionQuestionsUserFollowing.findAllByQId(_id)
            for(DiscussionQuestionsProperties discussionQuestionsProperties:qPropList){
                discussionQuestionsProperties.delete(flush:true)
            }
            for(DiscussionQuestionsUserFollowing discussionQuestionsUserFollowing:dUserFlwing){
                discussionQuestionsUserFollowing.delete(flush:true)
            }
            Integer pointsForAddingQuestion = DiscussionPointsMst.findById(discussionPointsMstIdForAddingQuestion).getPointsValue()
            def discussionUsers =  DiscussionBoardUser.findByUserId(questions.createdBy)

            List discussionBoardUserPoints = DiscussionBoardUserPoints.findAllByQuestionId(_id)
            for(DiscussionBoardUserPoints discussionBoardUserPoint:discussionBoardUserPoints){
                discussionBoardUserPoint.delete(flush:true)
            }
            discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingQuestion
            discussionUsers.save(flush:true)
        }
        discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)

        discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
        discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId))
        discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
        discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
        return ['message':'Questions deleted']
    }

    def updateAdminMultipleSelect(params,def request,def clientName,def siteName,def serverUrl){
        List<Long> ids = request.JSON.ids
        String action = request.JSON.action , uStr = ""
        if(action.equals("notIncorrect")) {
            List<DiscussionQuestionsProperties> discussionQuestionsProperties = DiscussionQuestionsProperties.findAllByDiscussionQuestionIdInListAndDiscussionBoardPropertiesId(ids,discussionPropertiesMstIdForIncorrect)
            for(DiscussionQuestionsProperties q:discussionQuestionsProperties) q.delete(flush:true);
            List<DiscussionQuestions> questions = DiscussionQuestions.findAllByIdInList(ids)
            for(DiscussionQuestions q:questions) {
                q.showQuestion = true
                q.save(flush:true)
            }
//            uStr = "\$unset:{\"incorrect\":''}"
        }
        else if(action.equals("notAbusive")) {
//            uStr = "\$unset: { \"abuse\": ''}"
            List<DiscussionQuestionsProperties> discussionQuestionsProperties = DiscussionQuestionsProperties.findAllByDiscussionQuestionIdInListAndDiscussionBoardPropertiesId(ids,discussionPropertiesMstIdForAbuse)
            for(DiscussionQuestionsProperties q:discussionQuestionsProperties) q.delete(flush:true);
            List<DiscussionQuestions> questions = DiscussionQuestions.findAllByIdInList(ids)
            for(DiscussionQuestions q:questions) {
                q.showQuestion = true
                q.save(flush:true)
            }
        }
        else if(action.equals("moderate")) {
//            uStr = "\$set:{\"show\":true}"
            List<DiscussionQuestions> questions = DiscussionQuestions.findAllByIdInList(ids)
            for(DiscussionQuestions q:questions) {
                q.showQuestion = true
                q.save(flush:true)
            }
        }
        else if(action.equals("moderateAnswers")) {
//            uStr = "\$set:{\"qaList.\$[].show\":true}"
            List<DiscussionAnswers> answers = DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids)
            for(DiscussionAnswers ans:answers) {
                ans.showAnswer = true
                ans.save(flush:true)
            }
        }
        else if(action.equals("notAbusiveAnswers")) {
//            uStr = "\$unset: { \"qaList.\$[].abuse\": ''}"
            List<DiscussionAnswers> answers = DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids)
            for(DiscussionAnswers ans:answers) {
                List<DiscussionAnswersProperties> answersProperties = DiscussionAnswersProperties.findAllByDiscussionAnswerIdAndDiscussionBoardPropertiesId(ans.id,discussionPropertiesMstIdForAbuse)
                for(DiscussionAnswersProperties a:answersProperties) a.delete(flush:true);
            }
            answers = DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids)
            for(DiscussionAnswers ans:answers) {
                ans.showAnswer = true
                ans.save(flush:true)
            }
        }
        else if(action.equals("notIncorrectAnswers")) {
//            uStr = "\$unset: { \"qaList.\$[].incorrect\": ''}"
            List<DiscussionAnswers> answers = DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids)
            for(DiscussionAnswers ans:answers) {
                List<DiscussionAnswersProperties> answersProperties = DiscussionAnswersProperties.findAllByDiscussionAnswerIdAndDiscussionBoardPropertiesId(ans.id,discussionPropertiesMstIdForIncorrect)
                for(DiscussionAnswersProperties a:answersProperties) a.delete(flush:true);
            }
            answers = DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids)
            for(DiscussionAnswers ans:answers) {
                ans.showAnswer = true
                ans.save(flush:true)
            }
        }
        else if(action.equals("deleteAnswers")) {
//            uStr = "\$pull: { \"qaList\": {show:false}}"
            List<DiscussionAnswers> answers = DiscussionAnswers.findAllByDiscussionQuestionIdInList(ids)
            for(DiscussionAnswers ans:answers) {
                User user = dataProviderService.getUserMst(ans.createdBy)
                List discussinAnswerProperties = DiscussionAnswersProperties.findAllByDiscussionAnswerId(ans.id)
                List discussionUserPoints = DiscussionBoardUserPoints.findAllByAnswerId(ans.id)
                Integer pointsForAddingAnswer = DiscussionPointsMst.findById(discussionPointsMstIdForAddingAnswer).getPointsValue()
                def discussionUsers =  DiscussionBoardUser.findByUserId(ans.createdBy)
                discussionUsers.userTotalPoints = discussionUsers.userTotalPoints - pointsForAddingAnswer
                discussionUsers.save(flush:true)
                for(DiscussionAnswersProperties answersProperties:discussinAnswerProperties){
                    answersProperties.delete(flush:true)
                }
                for(DiscussionBoardUserPoints discussionBoardUserPoints:discussionUserPoints){
                    discussionBoardUserPoints.delete(flush:true)
                }
                ans.delete(flush:true)
                if(user != null && user.email != null) sendEmail(user.email,user.name, "Your Answer is Deleted","Answer Deleted", Integer.parseInt(request.JSON.siteId), siteName,clientName, "")
            }
        }
//        else if(action.equals("delete")) uStr = ""
//        List idList = new ArrayList()
//        if(ids == null) ids = new ArrayList()
//        StringBuffer sb = new StringBuffer()
//        sb.append("[")
//        ids.each { q ->
//            def _id = new ObjectId(Integer.parseInt(q.split(",")[0]), Integer.parseInt(q.split(",")[1]), (short) Short.parseShort(q.split(",")[2]), Integer.parseInt(q.split(",")[3]))
//            sb.append("ObjectId(\"")
//            sb.append(_id)
//            sb.append("\"),")
//            idList.add(_id)
//        }
        //sb.append("]")
        if(action.equals("moderate")){
            for(int m=0;m<ids.size();m++){
                DiscussionQuestions questions = DiscussionQuestions.findById(ids.get(m))
                String questionLink = serverUrl + "/discussion/discussionBoard?qId="+questions.id+"&open=questionExplorer"
                User qUsr = User.findByUsername(questions.createdBy)
                if(qUsr != null && qUsr.email != null && !qUsr.email.isEmpty()) sendEmail(qUsr.email,qUsr.name, "Your Question Approved.","Question Approved", Integer.parseInt(request.JSON.siteId), siteName,clientName, questionLink)
            }
        }else if(action.equals("moderateAnswers")){
            List<DiscussionQuestions> questions = DiscussionQuestions.findAllByIdInList(ids)
            for(DiscussionQuestions discussionQuestions:questions){
                List<DiscussionAnswers> answers = DiscussionAnswers.findAllByDiscussionQuestionIdAndShowAnswer(discussionQuestions.id,true)
                User qUsr = User.findByUsername(discussionQuestions.createdBy)
                if(answers.size() < 3 && qUsr != null && qUsr.email != null && !qUsr.email.isEmpty()) {
                    String questionLink = serverUrl + "/discussion/discussionBoard?qId="+questions.id+"&open=questionExplorer"
                    sendEmail(qUsr.email,qUsr.name, "Your Question has been Answered. ","Question Answered", Integer.parseInt(request.JSON.siteId), siteName,clientName, questionLink)
                }
            }
//            for(int m=0;m<idList.size();m++){
//                DiscussionQuestions questions = DiscussionQuestions.findById(idList.get(m))
//                int count = 0;
//                for(int i=0; i<questions.qaList.size();i++){
//                    if(questions.qaList.get(i).show) count ++;
//                    if(count > 3) break;
//                }
//                String questionLink = serverUrl + "/discussion/discussionBoard?qId="+questions.id.timestamp+"__"+questions.id.machineIdentifier+"__"+questions.id.processIdentifier+"__"+questions.id.counter+"&open=questionExplorer"
//                if(questions.email != null && !questions.email.isEmpty() && count<3) {
//                    sendEmail(questions.email,questions.userName, "Your Question has been Answered. ","Question Answered", Integer.parseInt(request.JSON.siteId), siteName,clientName, questionLink)
//                }
//
//            }
        }
        if(true){
//            def database = mongo.getDatabase("wsdiscussion")
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"_id\": { \$in: " +
//                    sb.toString() +
//                    " }}," +
//                    " u: { " +
//                    uStr +
//                    " }," +
//                    " multi:true" +
//                    "         }," +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
            if(action.equals("notIncorrect")) {
                discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            }
            else if(action.equals("notAbusive")) {
                discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            }
            else if(action.equals("moderate")) {
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)

                discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            }else if(action.equals("moderateAnswers") || action.equals("deleteAnswers")) {
                discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId))
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)

            } else if(action.equals("notAbusiveAnswers") || action.equals("deleteAnswers")) {
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
            }
            else if(action.equals("notIncorrectAnswers") || action.equals("deleteAnswers")) {
                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
                discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
            }
//            else if( action.equals("deleteAnswers")) {
//                discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
//                discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
//                discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
//            }
            return ['message':'Questions and Answers are updated']
        }else {
            return ['error':'Question is archived. Question cannot be raised as abused']
        }
    }


    def getSubjectDrList(){
        List<SubjectMst> subjectMstList = SubjectMst.findAll()
        return subjectMstList
    }


    def updateRecentAnsweredDiscussionQuestionCache(params,def siteId){
        def questions = discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,siteId,0)
        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,0)
        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,0)
        return questions
    }

    Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss";
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern);
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone));

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern);
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone));
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom));
        return dateTo;
    }

    def sendEmail(String toEmail,String fullname, String message, String dynamicSubjects, Integer siteId, String siteName, String clientName, String questionLink){

        if("<EMAIL>"!=toEmail && userManagementService.validateEmail(toEmail,siteId)) {
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from "Wonderslate <<EMAIL>>"
                    subject ""+dynamicSubjects
                    body(view: "/discussion/emailTemplate",
                            model: [name: fullname, account: toEmail, siteName: siteName, clientName: clientName,message: message,questionLink:questionLink])
                }
            }catch (Exception e) {
                println("Exception in sending Discussion Board to " + toEmail + " and exception is " + e.toString())
            }
        }
    }

    def pushQuestionCreatedDateIntoQaList(){
//        def database = mongo.getDatabase("wsdiscussion");
//        List discussionQuestions = DiscussionQuestions.findAll()
//        discussionQuestions.each { q->
//            def _id = q.id
//            SimpleDateFormat f = new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss z");
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\")}, u: {  \$push:{\"qaList\":{\"createdOn\": new Date(\""+f.format(_id.date)+"\")} }}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
////            println(buildInfoResults)
//        }
    }

    def duplicateQuestion(params,def request){
        try{
            Long _id = request.JSON.id
//            def database = mongo.getDatabase("wsdiscussion")
//            String comand ="{" +
//                    "      delete: \"discussionQuestions\"," +
//                    "      deletes: [" +
//                    "         {" +
//                    "           q: {\"_id\": ObjectId(\"" +
//                    _id +
//                    "\") }, limit: 1," +
//                    "         }," +
//                    "      ]" +
//                    "   }"
            DiscussionQuestions.findById(_id).delete(flush:true)
            discussionBoardCacheService.updateAbuseDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)

            discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))

            return 0
        }catch(Exception ex){
            return 1
        }
    }

    def duplicateAnswer(params,def request){
        try{
            String usrName = ""
            def _id = request.JSON.answerId
            DiscussionAnswers answers = DiscussionAnswers.findById(_id)
            answers.delete(flush:true)
            discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(Integer.parseInt(request.JSON.siteId));
            discussionBoardCacheService.updateDiscussionAbuseAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId))
            discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(params,Integer.parseInt(request.JSON.siteId),0)
            return buildInfoResults
        }catch(Exception ex){
            return 1
        }
    }

    def updateUserDetails(String userName, long siteId){
            User user = User.findByUsername(userName)
//            def database = mongo.getDatabase("wsdiscussion");
//            String q = "",u="";
//            q =  " q: {\"userId\": \"" +
//                    user.username +
//                    "\"},"
//
//            u = " u: { \$set: {" +
//                    " \"userName\":\""+user.name +"\"," +
//                    " \"userId\":\""+user.username +"\"," +
//                    " \"uImgName\":\""+user.profilepic +"\"," +
//                    " \"email\":\""+user.email +"\"," +
//                    " \"uImgId\":"+user.id +"," +
//                    " } }," +
//                    "  multi:true"
//            String comand ="{" +
//                    "      update: \"discussionQuestions\"," +
//                    "      updates: [" +
//                    "         {" +
//                    q +
//                    u +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults = database.runCommand(new Document(Document.parse(comand)));
//
//        q =  " q: {\"qaList.userId\": \"" +
//                user.username +
//                "\"},"
//        u = " u: {  \$set: { " +
//                "\"qaList.\$[].uImgId\":" + user.id + "," +
//                "\"qaList.\$[].uImgName\":\"" + user.profilepic + "\"," +
//                "\"qaList.\$[].userId\":\"" + user.username + "\"," +
//                "\"qaList.\$[].userName\":\"" + user.name + "\"" +
//                " } }," +
//                "  multi:true"
//
//        comand ="{" +
//                "      update: \"discussionQuestions\"," +
//                "      updates: [" +
//                "         {" +
//                q +
//                u +
//                "         }" +
//                "      ]" +
//                "   }"
//        Document buildInfoResults2 = database.runCommand(new Document(Document.parse(comand)));
//
//            comand ="{" +
//                    "      update: \"discussionUsers\"," +
//                    "      updates: [" +
//                    "         {" +
//                    "           q: {\"userid\": \"" +
//                    user.username +
//                    "\"}, u: { \$set: {\"userName\":\""+user.name+"\"}}" +
//                    "         }" +
//                    "      ]" +
//                    "   }"
//            Document buildInfoResults1 = database.runCommand(new Document(Document.parse(comand)));
//        discussionBoardCacheService.updateAbuseDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateRecentAnsweredDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateRecentAnsweredFilterDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateRecentUnAnsweredFilterDiscussionQuestionCache(siteId,0)
//
//        discussionBoardCacheService.updateUnModeratedDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateUnAnsweredDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateUnTagedDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateIncorrectDiscussionQuestionCache(siteId,0)
//        discussionBoardCacheService.updateUnModeratedDiscussionAnswersCache(siteId)
//        discussionBoardCacheService.updateDiscussionAbuseAnswersCache(siteId)
//        discussionBoardCacheService.updateDiscussionIncorrectAnswersCache(siteId)
            return []
    }
}
