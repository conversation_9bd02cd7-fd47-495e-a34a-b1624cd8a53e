package com.wonderslate.logs

import com.wonderslate.log.DisCodeLog
import com.wonderslate.shop.DiscountMst
import grails.transaction.Transactional

@Transactional
class DiscodeService {

    def trackDiscountCodeDisplay(String disCode, String username, String sessionId, Integer siteId, String ipAddress, String userAgent, String pageName) {
        try {
            DisCodeLog disCodeLog = new DisCodeLog(
                disCode: disCode,
                username: username,
                sessionId: sessionId,
                siteId: siteId,
                ipAddress: ipAddress,
                userAgent: userAgent,
                pageName: pageName,
                dateCreated: new Date()
            )

            disCodeLog.save(failOnError: true, flush: true)

            log.info("Discount code display tracked: disCode=${disCode}, username=${username}, sessionId=${sessionId}, siteId=${siteId}, pageName=${pageName}")

            return disCodeLog
        } catch (Exception e) {
            log.error("Error saving discount code tracking: " + e.getMessage(), e)
            throw e
        }
    }
    
    def hasDiscountCodeBeenShownInSession(String sessionId, String disCode) {
        try {
            if (!sessionId || !disCode) {
                return false
            }
            
            DisCodeLog existingLog = DisCodeLog.findBySessionIdAndDisCode(sessionId, disCode)
            return existingLog != null
        } catch (Exception e) {
            log.error("Error checking if discount code was shown in session: " + e.getMessage(), e)
            return false
        }
    }

    /**
     * Get discount name by coupon code and site ID from DiscountMst table
     */
    def getDiscountNameByCouponCodeAndSiteId(String couponCode, Integer siteId) {
        try {
            if (!couponCode || !siteId) {
                return null
            }

            DiscountMst discountMst = DiscountMst.findByCouponCodeAndSiteId(couponCode, siteId)
            return discountMst?.name
        } catch (Exception e) {
            log.error("Error getting discount name by coupon code and site ID: " + e.getMessage(), e)
            return null
        }
    }
}
