package com.wonderslate.data

import com.wonderslate.logs.AutogptErrorLoggerService
import com.wonderslate.shop.PdfExporterService
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.grails.web.json.JSONObject
import org.springframework.transaction.annotation.Propagation
import java.util.concurrent.*
import grails.async.Promises
import static grails.async.Promises.task
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.StringEntity
import org.apache.http.util.EntityUtils
import javax.annotation.PreDestroy

class TheoryBooksService {
    def grailsApplication
    PdfExporterService pdfExporterService
    def springSecurityService
    AutogptService autogptService
    AutogptErrorLoggerService autogptErrorLoggerService
    def redisService

    // Configuration constants
    private static final int QUESTIONS_PER_BATCH = 20
    private static final int MAX_PARALLEL_TASKS = 5

    // Connection pool for HTTP requests - no timeouts for LLM calls
    private static final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager()
    private static final CloseableHttpClient httpClient

    // Thread pool for async operations
    private final ExecutorService executorService = Executors.newFixedThreadPool(3)

    // Cache for frequently accessed data
    private final Map<String, Prompts> promptsCache = new ConcurrentHashMap<>()

    static {
        connectionManager.setMaxTotal(20)
        connectionManager.setDefaultMaxPerRoute(5)

        httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .disableConnectionState() // For better performance
            .build()
    }

    def talkToGPT(String prompt, String apiEndpoint = "chat-completion") {
        try {
            String url = grailsApplication.config.grails.aiserver.url + "/" + apiEndpoint
            HttpPost httpPost = new HttpPost(url)

            // Set headers
            httpPost.setHeader("Content-Type", "application/json")

            // Create request body
            JSONObject requestBody = new JSONObject()
            requestBody.put("prompt", prompt)
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8")
            httpPost.setEntity(entity)

            // Execute request with no timeout (LLM responses can be very long)
            def response = httpClient.execute(httpPost)

            try {
                if (response.getStatusLine().getStatusCode() == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity())
                    def jsonSlurper = new JsonSlurper()
                    try {
                        def jsonResponse = jsonSlurper.parseText(responseBody)
                        return jsonResponse
                    } catch (Exception e) {
                        println("Exception parsing GPT response: " + (e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString()))
                        return null
                    }
                } else {
                    println("Error in GPT call: " + response.getStatusLine().getStatusCode())
                    return null
                }
            } finally {
                response.close()
            }
        } catch (Exception e) {
            println("Exception in talkToGPT: " + (e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString()))
            return null
        }
    }

    def jsonCleaner(String jsonInput){
        if (!jsonInput) return jsonInput

        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("html", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")

        return jsonInput
    }

    // Cache prompts to avoid repeated DB queries
    private Prompts getCachedPrompt(String promptType) {
        return promptsCache.computeIfAbsent(promptType) {
            Prompts.findByPromptType(promptType)
        }
    }

    // Batch fetch related data to avoid N+1 queries
    private Map<String, Object> fetchChapterData(String chapterId) {
        // Skip Redis caching for now to avoid serialization issues
        // Can be re-enabled later with proper serialization handling
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(chapterId))
        BooksDtl booksDtl = BooksDtl.findByBookId(chaptersMst.bookId)
        List<ChaptersSubtopicDtl> subtopics = ChaptersSubtopicDtl.findAllByChapterId(new Long(chapterId))

        return [
            chapter: chaptersMst,
            book: booksDtl,
            subtopics: subtopics
        ]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def createChapterContents(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch all related data in one go to avoid N+1 queries
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book
        List<ChaptersSubtopicDtl> subtopics = chapterData.subtopics

        // Cache prompts
        Prompts subtopicPrompts = getCachedPrompt("subtopicContentCreator")

        File srcDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/"+chaptersMst.bookId)
        if(!srcDir.exists()) srcDir.mkdirs()
        File chapterHtmlFile = new File(srcDir, "chapter_"+chapterId+".html")

        // Pre-fetch PYQ resource to avoid repeated queries
        ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")

        // Use StringBuilder for efficient string concatenation
        StringBuilder htmlContentBuilder = new StringBuilder()

        // Prepare data for LLM calls in main thread (with database access)
        def subtopicData = subtopics.collect { subtopic ->
            String questionText = ""
            if (pyqResourceDtl) {
                List<ObjectiveMst> questions = ObjectiveMst.findAllByQuizIdAndSubTopic(new Integer(pyqResourceDtl.resLink), subtopic.title)
                StringBuilder questionBuilder = new StringBuilder()
                questions.eachWithIndex { question, index ->
                    questionBuilder.append("${index + 1}. ${question.question}\n")
                }
                questionText = questionBuilder.toString()
            }

            String prompt = subtopicPrompts.basePrompt
            prompt = prompt.replaceAll("SUBTOPIC", subtopic.title+" "+subtopic.keyConcepts+". "+subtopic.learningObjective)
            prompt = prompt + "\n PYQs: \n" + questionText

            return [
                subtopic: subtopic,
                prompt: prompt,
                subtopicTitle: subtopic.title
            ]
        }

        // Process LLM calls in parallel (no database operations in async threads)
        def llmPromises = subtopicData.collect { data ->
            task {
                try {
                    println("Processing subtopic: " + data.subtopicTitle)
                    println("Calling GPT for subtopic: " + data.subtopicTitle)

                    // Only LLM call in async thread - no database operations
                    def responseString = talkToGPT(data.prompt)

                    return [
                        success: true,
                        subtopic: data.subtopic,
                        response: responseString?.response,
                        subtopicTitle: data.subtopicTitle
                    ]
                } catch (Exception e) {
                    println("Error processing subtopic ${data.subtopicTitle}: ${e.message}")
                    return [
                        success: false,
                        subtopic: data.subtopic,
                        error: e.message,
                        subtopicTitle: data.subtopicTitle
                    ]
                }
            }
        }

        // Wait for all LLM calls to complete
        def llmResults = llmPromises.collect { it.get() }

        // Process results sequentially in main session (database operations)
        llmResults.each { result ->
            if (result.success && result.response) {
                try {
                    String content = jsonCleaner(result.response)
                    htmlContentBuilder.append(content)
                    println("Successfully processed subtopic: ${result.subtopicTitle}")
                } catch (Exception e) {
                    println("Error processing result for subtopic ${result.subtopicTitle}: ${e.message}")
                }
            } else {
                println("Failed to process subtopic: ${result.subtopicTitle} - ${result.error ?: 'No response'}")
            }
        }

        // Generate introduction synchronously to avoid session issues
        Prompts introPrompts = getCachedPrompt("chapterIntroductionCreator")
        String introPrompt = introPrompts.basePrompt
        introPrompt = introPrompt.replaceAll("CHAPTERTITLE", chaptersMst.name)
        introPrompt = introPrompt + " \n" + htmlContentBuilder.toString()
        println("Calling GPT for introduction")
        def responseString = talkToGPT(introPrompt)
        String introContent = responseString?.response ? jsonCleaner(responseString.response) : ""

        String finalHtmlContent = introContent + htmlContentBuilder.toString()

        // Create resource record (database operation - keep in main thread)
        resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl==null) {
            resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "Notes", resourceName: chaptersMst.name, createdBy:"System" , resLink: "blank")
            resourceDtl.save(failOnError: true, flush: true)
        }

        // File operations can be done synchronously for now to avoid session issues
        chapterHtmlFile.write(finalHtmlContent)

        // PDF generation - keep synchronous to avoid session issues
        println("Generating PDF for chapter: " + chapterId)
        pdfExporterService.generateTheoryBookPdf(""+chapterId,""+resourceDtl.id)
        println("PDF generated for chapter: " + chapterId)

        resourceDtl.resLink = "supload/books/"+chaptersMst.bookId+"/chapters/"+chapterId+"/"+resourceDtl.id+"/"+resourceDtl.id+".pdf"
        resourceDtl.filename = resourceDtl.id+".pdf"
        resourceDtl.save(failOnError: true, flush: true)

        return [status: "OK"]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def getPYQsForChapter(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch all related data efficiently
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book
        List<ChaptersSubtopicDtl> subtopics = chapterData.subtopics

        Prompts prompts
        if("true".equals(params.entranceExams)) prompts = getCachedPrompt("pyqsForEntranceExams")
        else prompts = getCachedPrompt("pyqsForSubtopic")
        // Create resource once outside the loop
        QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
        quizIdGenerator.save()
        resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "QA", resourceName: "PYQs",
                resLink: quizIdGenerator.id, createdBy: "System")
        resourceDtl.save(failOnError: true, flush: true)

        // Prepare data for LLM calls in main thread (with database access)
        def subtopicData = subtopics.collect { subtopic ->
            String prompt = prompts.basePrompt
            if("true".equals(params.entranceExams)){
                prompt = prompt.replaceAll("TARGETEXAM", booksDtl.university ?: "")
            }
            else {
                prompt = prompt.replaceAll("UNIVERSITY", booksDtl.university ?: "")
            }
            prompt = prompt.replaceAll("SYLLABUSSUBJECT", booksDtl.syllabusSubject ?: "")
            prompt = prompt.replaceAll("SUBTOPIC", subtopic.title+" "+subtopic.keyConcepts)

            return [
                subtopic: subtopic,
                prompt: prompt,
                subtopicTitle: subtopic.title
            ]
        }

        // Process LLM calls in parallel (no database operations in async threads)
        def llmPromises = subtopicData.collect { data ->
            task {
                try {
                    // Only LLM call in async thread - no database operations
                    def responseString = talkToGPT(data.prompt,"perplexity-completion")

                    return [
                        success: true,
                        subtopic: data.subtopic,
                        response: responseString?.response,
                        subtopicTitle: data.subtopicTitle
                    ]
                } catch (Exception e) {
                    println("Error processing subtopic ${data.subtopicTitle}: ${e.message}")
                    return [
                        success: false,
                        subtopic: data.subtopic,
                        error: e.message,
                        subtopicTitle: data.subtopicTitle
                    ]
                }
            }
        }

        // Wait for all LLM calls to complete
        def llmResults = llmPromises.collect { it.get() }

        // Process results sequentially in main session (database operations)
        def totalQuestions = 0
        llmResults.each { result ->
            if (result.success && result.response) {
                try {
                    def jsonResponse
                    try {
                        jsonResponse = new JsonSlurper().parseText(jsonCleaner(result.response))
                    } catch (Exception e) {
                        try {
                            jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(result.response))
                        } catch (Exception e2) {
                            println("Failed to parse JSON for subtopic ${result.subtopicTitle}: ${e2.toString()}")
                            return
                        }
                    }

                    // Insert questions for this subtopic (database operations in main session)
                    jsonResponse.each { question ->
                        println("the response is "+question)
                        ObjectiveMst objectiveMst = new ObjectiveMst(
                            quizId: new Integer(resourceDtl.resLink),
                            quizType: "QA",
                            question: question.question,
                            difficultylevel: question.difficulty,
                            qType: question.type,
                            subTopic: result.subtopic.title,
                            explainLink: "${question.university ?: ''},${question.year ?: ''}"
                        )
                        objectiveMst.save(failOnError: true, flush: false) // Batch flush
                        totalQuestions++
                    }
                    println("Successfully processed ${jsonResponse.size()} questions for subtopic: ${result.subtopicTitle}")
                } catch (Exception e) {
                    println("Error processing result for subtopic ${result.subtopicTitle}: ${e.message}")
                }
            } else {
                println("Failed to process subtopic: ${result.subtopicTitle} - ${result.error ?: 'No response'}")
            }
        }

        // Flush all saves at once
        ObjectiveMst.withSession { session ->
            session.flush()
        }

        println("Total questions created: ${totalQuestions}")
        return [status: "OK", noOfQuestions: totalQuestions]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def getPYQsForBoard(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch chapter data efficiently
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book

        ResourceDtl readingMaterialResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Long(chapterId),"Notes")
        String extractPath = readingMaterialResourceDtl?.extractPath
        println("extractPath: for chapter ${chapterId} is " + extractPath)

        if (!extractPath || extractPath.trim().isEmpty()) {
            println("extractPath is empty for chapter ${chapterId} and calling storePdfVectors")
            autogptService.storePdfVectors(params)
            extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+readingMaterialResourceDtl.id+"/extractedImages/"+readingMaterialResourceDtl.id+".txt"
        }

        String folderPath = extractPath.substring(0, extractPath.lastIndexOf("/"))
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/chapterMetadata" + chaptersMst.id + ".txt")
        if(!metadataFile.exists()) {
            autogptService.getChapterMetaData(params)
        }

        // Parse metadata with better error handling
        def json
        try {
            String metadataString = metadataFile.text
            metadataString = jsonCleaner(metadataString)
            json = new JsonSlurper().parseText(metadataString)
        } catch (Exception e) {
            try {
                String metadataString = metadataFile.text
                metadataString = autogptService.fixJSONFromLLM(metadataString)
                json = new JsonSlurper().parseText(metadataString)
            } catch (Exception e2) {
                println("Failed to parse metadata for chapter ${chapterId}: ${e2.toString()}")
                autogptErrorLoggerService.createLog(new Long(chapterId), null, "getPYQsForBoard", "Exception in parsing metadata", metadataFile.text)
                return [status: "Error", message: "Failed to parse chapter metadata"]
            }
        }

        def subtopics = json.metadata?.subtopics
        if (!subtopics) {
            println("No subtopics found in metadata for chapter " + chapterId)
            return [status: "Error", message: "No subtopics found in metadata"]
        }

        println("No of subtopics are "+subtopics.size()+" for chapter " + chaptersMst.id)
        Prompts prompts = getCachedPrompt("pyqsForBoardExams")

        // Clean up existing PYQs if they exist
        ResourceDtl.withSession { session ->
            session.clear()
        }
        resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) {
            println("PYQs already created for chapter " + chapterId+" and we will delete them first")
            ObjectiveMst.executeUpdate("delete from ObjectiveMst where quizId = :quizId", [quizId: resourceDtl.resLink])
        }

        // Create resource once outside the loop
        if (resourceDtl == null) {
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()
            resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "QA", resourceName: "PYQs",
                    resLink: quizIdGenerator.id, createdBy: "System")
            resourceDtl.save(failOnError: true, flush: true)
        }

        def totalQuestions = 0
        StringBuilder htmlContentBuilder = new StringBuilder()

        // Prepare data for LLM calls in main thread (with database access)
        def subtopicData = subtopics.collect { subtopic ->
            String prompt = prompts.basePrompt
            prompt = prompt.replaceAll("SYLLABUSSUBJECT", params.subjectSyllabus ?: "")
            prompt = prompt.replaceAll("TARGETBOARD", params.boardExam ?: "")
            prompt = prompt + " " + (subtopic.heading ?: "") + " " + (subtopic.keyConcepts ?: "")

            return [
                subtopic: subtopic,
                prompt: prompt,
                subtopicHeading: subtopic.heading ?: "",
                subtopicKeyConcepts: subtopic.keyConcepts ?: ""
            ]
        }

        // Process LLM calls in parallel (no database operations in async threads)
        def llmPromises = subtopicData.collect { data ->
            task {
                try {
                    // Only LLM call in async thread - no database operations
                    def responseString = talkToGPT(data.prompt, "perplexity-completion")

                    return [
                        success: true,
                        subtopic: data.subtopic,
                        response: responseString?.response,
                        subtopicHeading: data.subtopicHeading,
                        subtopicKeyConcepts: data.subtopicKeyConcepts
                    ]
                } catch (Exception e) {
                    println("Exception in getPYQsForBoard for subtopic ${data.subtopicHeading} and chapter ${chapterId}: ${e.toString()}")
                    return [
                        success: false,
                        subtopic: data.subtopic,
                        error: e.message,
                        subtopicHeading: data.subtopicHeading
                    ]
                }
            }
        }

        // Wait for all LLM calls to complete
        def llmResults = llmPromises.collect { it.get() }

        // Process results sequentially in main session (database operations)
        llmResults.each { result ->
            if (result.success && result.response) {
                try {
                    def jsonResponse
                    try {
                        jsonResponse = new JsonSlurper().parseText(jsonCleaner(result.response))
                    } catch (Exception e) {
                        try {
                            jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(result.response))
                        } catch (Exception e2) {
                            autogptErrorLoggerService.createLog(new Long(chapterId), resourceDtl.id, "pyqsForBoardExams", "Exception in parsing responseAnswer", result.response)
                            println("Failed to parse JSON for subtopic ${result.subtopicHeading}: ${e2.toString()}")
                            return
                        }
                    }

                    // Prepare questions and content (database operations in main session)
                    StringBuilder questionsBuilder = new StringBuilder()
                    int questionNumber = 1

                    jsonResponse.each { question ->
                        questionsBuilder.append("${questionNumber}. ${question.question}\n")
                        ObjectiveMst objectiveMst = new ObjectiveMst(
                            quizId: new Integer(resourceDtl.resLink),
                            quizType: "QA",
                            question: question.question,
                            difficultylevel: question.difficulty,
                            qType: question.type,
                            subTopic: result.subtopic.title,
                            explainLink: "${question.board ?: ''},${question.year ?: ''}"
                        )
                        objectiveMst.save(failOnError: true, flush: false)
                        totalQuestions++
                        questionNumber++
                    }

                    String questionsText = questionsBuilder.toString()
                    String subtopicContent = result.subtopicHeading + " " + result.subtopicKeyConcepts

                    // Generate enhanced metadata and content
                    String enhancedMetadata = createEnhancedMetadata(questionsText, subtopicContent)
                    String htmlContent = createSubTopicContents(subtopicContent, questionsText, enhancedMetadata)

                    htmlContentBuilder.append(htmlContent)
                    println("Successfully processed subtopic: ${result.subtopicHeading} with ${jsonResponse.size()} questions")
                } catch (Exception e) {
                    println("Error processing result for subtopic ${result.subtopicHeading}: ${e.message}")
                }
            } else {
                println("Failed to process subtopic: ${result.subtopicHeading} - ${result.error ?: 'No response'}")
            }
        }

        // Flush all database operations
        ObjectiveMst.withSession { session ->
            session.flush()
        }
        // File operations - keep synchronous to avoid session issues
        String finalHtmlContent = htmlContentBuilder.toString()
        File srcDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/"+chaptersMst.bookId)
        if(!srcDir.exists()) srcDir.mkdirs()
        File chapterHtmlFile = new File(srcDir, "chapter_"+chapterId+".html")

        chapterHtmlFile.write(finalHtmlContent)

        // Create ExamNotes resource (database operation - keep in main thread)
        ResourceDtl examNotesResourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"ExamNotes")
        if(examNotesResourceDtl==null) {
            examNotesResourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "ExamNotes", resourceName: chaptersMst.name, createdBy: "System", resLink: "blank")
            examNotesResourceDtl.save(failOnError: true, flush: true)
        }

        // Generate PDF synchronously to avoid session issues
        println("Generating PDF for chapter "+chaptersMst.name)
        pdfExporterService.generateTheoryBookPdf(""+chapterId,""+examNotesResourceDtl.id)

        examNotesResourceDtl.resLink = "supload/books/"+chaptersMst.bookId+"/chapters/"+chapterId+"/"+examNotesResourceDtl.id+"/"+examNotesResourceDtl.id+".pdf"
        examNotesResourceDtl.filename = examNotesResourceDtl.id+".pdf"
        examNotesResourceDtl.save(failOnError: true, flush: true)

        println("PYQs created for chapter "+chapterId+" and no of questions are "+totalQuestions)
        return [status: "OK", noOfQuestions: totalQuestions]
    }

    def createEnhancedMetadata(String pyqQuestions,String subtopicContents){
        Prompts prompts = getCachedPrompt("metaDataEnhancer")
        String prompt = prompts.basePrompt
        prompt += "\n"+subtopicContents
        prompt += "\n The PYQs are"+pyqQuestions
        def responseString = talkToGPT(prompt)
        return responseString?.response ? jsonCleaner(responseString.response) : ""
    }

    def createSubTopicContents(String subtopicContents,String pyqQuestions,String enhancedMetadata){
        Prompts prompts = getCachedPrompt("contentCreatorForEntranceExamSubtopic")
        String prompt = prompts.basePrompt
        prompt = prompt + "\n"+subtopicContents
        prompt += "\n The PYQs are "+pyqQuestions
        prompt += "\n The PYQ pattern is "+enhancedMetadata
        def responseString = talkToGPT(prompt)
        return responseString?.response ? jsonCleaner(responseString.response) : ""
    }

    def createEnhancedMetadataForEntranceExam(String pyqQuestions,String enhancedQuestions,String subtopic,String university,String keyConcepts){
        Prompts prompts = getCachedPrompt("metdataEnhancerForEntranceExam")
        String prompt = prompts.basePrompt
        prompt = prompt.replaceAll("SUBTOPIC", subtopic)
        prompt = prompt.replaceAll("TARGETEXAM", university!=null?university: "")
        prompt = prompt.replaceAll("KEYCONCEPTS", keyConcepts)
        prompt += "\n The PYQs are "+pyqQuestions +"\n The enhanced questions are "+enhancedQuestions
        def responseString = talkToGPT(prompt)
        return responseString?.response ? jsonCleaner(responseString.response) : ""
    }

    def createSubTopicContentsForEntranceExam(String subtopicContents,String pyqQuestions,String enhancedMetadata,String enhancedQuestions,String subtopic,String university,String keyConcepts){
        Prompts prompts = getCachedPrompt("examNotesForSubtopic")
        String prompt = prompts.basePrompt
        prompt = prompt.replaceAll("SUBTOPIC", subtopic)
        prompt = prompt.replaceAll("TARGETEXAM", university!=null?university: "")
        prompt = prompt.replaceAll("KEYCONCEPTS", keyConcepts)
        prompt = prompt + "\n"+subtopicContents
        prompt += "\n The PYQs are "+pyqQuestions
        prompt += "\n The Enhanced questions are "+enhancedQuestions
        prompt += "\n The metadata is  "+enhancedMetadata
        def responseString = talkToGPT(prompt)
        return responseString?.response ? jsonCleaner(responseString.response) : ""
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def createEnhancedPYQsForEntranceExams(params) {
        println("createEnhancedPYQsForEntranceExams called for chapter " + params.chapterId)
        String chapterId = params.chapterId

        // Check if enhanced PYQs already exist
        ResourceDtl enhancedResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId), "QA", "EnhancedPYQs")
        if (enhancedResourceDtl != null) {
            return [status: "OK", message: "Enhanced PYQs already exist"]
        }else{
            //check if QuestionBank QnA or QuestionBank MCQs exists and return error if not
            ResourceDtl questionBankResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(new Long(chapterId), "QuestionBank%")
            if (questionBankResourceDtl != null) {
                return [status: "OK", message: "Enhanced PYQs already exist"]
            }
        }
        println("Enhanced PYQs not found for chapter " + chapterId)
        // Get the original PYQs resource
        ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId), "QA", "PYQs")
        if (pyqResourceDtl == null) {
            return [status: "Error", message: "Original PYQs not found"]
        }
        println("Original PYQs found for chapter " + chapterId)

        // Get the prompt for enhancing PYQs
        Prompts enhancerPrompts = getCachedPrompt("pyqsEnhancerForEntranceExams")

        // Create new resource for enhanced PYQs
        QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
        quizIdGenerator.save()
        enhancedResourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "QA", resourceName: "EnhancedPYQs",
                resLink: quizIdGenerator.id, createdBy: "System")
        enhancedResourceDtl.save(failOnError: true, flush: true)

        int totalEnhancedQuestions = 0

        // Get all original questions for this chapter
        List<ObjectiveMst> allOriginalQuestions = ObjectiveMst.findAllByQuizId(new Integer(pyqResourceDtl.resLink))
        println("Total original questions found: " + allOriginalQuestions.size())
        if (allOriginalQuestions.size() > 0) {
            // Create batches of questions each
            List<List<ObjectiveMst>> batches = []
            for (int i = 0; i < allOriginalQuestions.size(); i += QUESTIONS_PER_BATCH) {
                int endIndex = Math.min(i + QUESTIONS_PER_BATCH, allOriginalQuestions.size())
                batches.add(allOriginalQuestions.subList(i, endIndex))
            }

            // Process batches in parallel using tasks
            def tasks = []
            int maxTasks = Math.min(MAX_PARALLEL_TASKS, batches.size())

            for (int i = 0; i < batches.size(); i += maxTasks) {
                def currentBatchTasks = []
                int endIndex = Math.min(i + maxTasks, batches.size())

                // Create tasks for current group
                for (int j = i; j < endIndex; j++) {
                    def batch = batches[j]
                    println("Creating task for batch " + (j + 1) + " of " + batches.size())
                    def batchTask = task {
                        return processEnhancedPYQBatchForTask(batch, enhancerPrompts.basePrompt, enhancedResourceDtl.resLink.toString())
                    }
                    currentBatchTasks.add(batchTask)
                }

                // Wait for current group of tasks to complete
                currentBatchTasks.each { batchTask ->
                    try {
                        def batchResult = batchTask.get()
                        totalEnhancedQuestions += batchResult.questionCount
                    } catch (Exception e) {
                        println("Error processing enhanced PYQ batch task: " + e.message)
                    }
                }
            }
        }

        println("Enhanced PYQs created for chapter " + chapterId + " with " + totalEnhancedQuestions + " questions")



        return [status: "OK", noOfQuestions: totalEnhancedQuestions]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def createExamNotesForEntranceExams(params) {
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch all related data efficiently
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book
        List<ChaptersSubtopicDtl> subtopics = chapterData.subtopics
        println("createExamNotesForEntranceExams called for chapter " + chapterId)
        StringBuilder htmlContentBuilder = new StringBuilder()
        //for each subtopic create enhanced metadata and content
        def subtopicData = subtopics.collect { subtopic ->
            //get the PYQs resource dtl and questions
            ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId), "QA", "PYQs")
            String questionsText = ""
            if (pyqResourceDtl) {
                List<ObjectiveMst> questions = ObjectiveMst.findAllByQuizIdAndSubTopic(new Integer(pyqResourceDtl.resLink), subtopic.title)
                StringBuilder questionBuilder = new StringBuilder()
                questions.eachWithIndex { question, index ->
                    questionBuilder.append("${index + 1}. ${question.question}\n")
                }
                questionsText = questionBuilder.toString()
            }
            //get the enhanced PYQs resource dtl and questions
            ResourceDtl enhancedPyqResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId), "QA", "EnhancedPYQs")
            String enhancedQuestionsText = ""
            if (enhancedPyqResourceDtl) {
                List<ObjectiveMst> questions = ObjectiveMst.findAllByQuizIdAndSubTopic(new Integer(enhancedPyqResourceDtl.resLink), subtopic.title)
                StringBuilder questionBuilder = new StringBuilder()
                questions.eachWithIndex { question, index ->
                    questionBuilder.append("${index + 1}. ${question.question}\n")
                }
                enhancedQuestionsText = questionBuilder.toString()
            }
            //call createEnhancedMetadataForEntranceExam
            String enhancedMetadata = createEnhancedMetadataForEntranceExam(questionsText,enhancedQuestionsText,subtopic.title,booksDtl.university,subtopic.title+" "+subtopic.keyConcepts)
            //call createSubTopicContentsForEntranceExam
            String htmlContent = createSubTopicContentsForEntranceExam(subtopic.title, questionsText, enhancedMetadata,enhancedQuestionsText,subtopic.title,booksDtl.university,subtopic.title+" "+subtopic.keyConcepts)

            htmlContentBuilder.append(htmlContent)

        }
        String finalHtmlContent = htmlContentBuilder.toString()
        File srcDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/"+chaptersMst.bookId)
        if(!srcDir.exists()) srcDir.mkdirs()
        File chapterHtmlFile = new File(srcDir, "chapter_"+chapterId+".html")

        chapterHtmlFile.write(finalHtmlContent)

        // Create ExamNotes resource (database operation - keep in main thread)
        ResourceDtl examNotesResourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(examNotesResourceDtl==null) {
            examNotesResourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "Notes", resourceName: chaptersMst.name, createdBy: "System", resLink: "blank")
            examNotesResourceDtl.save(failOnError: true, flush: true)
        }

        // Generate PDF synchronously to avoid session issues
        println("Generating PDF for chapter "+chaptersMst.name)
        pdfExporterService.generateTheoryBookPdf(""+chapterId,""+examNotesResourceDtl.id)

        examNotesResourceDtl.resLink = "supload/books/"+chaptersMst.bookId+"/chapters/"+chapterId+"/"+examNotesResourceDtl.id+"/"+examNotesResourceDtl.id+".pdf"
        examNotesResourceDtl.filename = examNotesResourceDtl.id+".pdf"
        examNotesResourceDtl.save(failOnError: true, flush: true)

        println("ExamNotes created for chapter "+chapterId)

        return [status: "OK"]
    }

    /**
     * Helper method to process a batch of questions for enhanced PYQs using tasks
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def processEnhancedPYQBatchForTask(List<ObjectiveMst> batch, String basePrompt, String resLink) {
        int batchQuestionCount = 0

        try {
            // Create prompt with batch questions
            String questionsText = batch.collect { ObjectiveMst question ->
                "Question: ${question.question}\nAnswer: ${question.answer}"
            }.join("\n\n")

            String enhancerPrompt = basePrompt + "\n\nOriginal Questions:\n" + questionsText

            // Call GPT to enhance questions
            def responseString = talkToGPT(enhancerPrompt)
            if (responseString?.response) {
                String cleanedResponse = jsonCleaner(responseString.response)
                try {
                    def jsonResponse
                    try {
                        jsonResponse = new JsonSlurper().parseText(cleanedResponse)
                    } catch (Exception e) {
                        println("Failed to parse JSON for batch: So calling fixJSONFromLLM")
                        jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(cleanedResponse))
                    }

                    // Process enhanced questions and collect data for batch save
                    List<Map> questionsToSave = []
                    jsonResponse.each { enhancedQuestion ->
                        questionsToSave.add([
                            quizId: new Integer(resLink),
                            quizType: "QA",
                            question: enhancedQuestion.question,
                            answer: enhancedQuestion.answer,
                            difficultylevel: enhancedQuestion.difficulty ?: "Medium",
                            questionType: enhancedQuestion.questionType ?: "Enhanced PYQ",
                            marks: new Double("1"),
                            qType: enhancedQuestion.type ?: "Enhanced PYQ",
                        ])
                    }

                    // Save all questions in this batch
                    questionsToSave.each { questionData ->
                        ObjectiveMst enhancedObj = new ObjectiveMst(questionData)
                        enhancedObj.save(failOnError: true, flush: true)
                        batchQuestionCount++
                    }

                } catch (Exception e) {
                    println("Error parsing enhanced questions response for batch: " + e.message)
                    println("Response was: " + cleanedResponse)
                }
            }
        } catch (Exception e) {
            println("Error processing enhanced PYQ batch: " + e.message)
        }

        return [questionCount: batchQuestionCount]
    }

    def addSolutionsToEnhancedPYQs(params) {
        String chapterId = params.chapterId

        // Find the enhanced PYQs resource
        ResourceDtl enhancedPYQsResource = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId), "QA", "EnhancedPYQs")
        if (!enhancedPYQsResource) {
            return [status: "Error", message: "Enhanced PYQs not found"]
        }

        // Step 2: Get or create ResourceDtl for MCQs in its own transaction
        ResourceDtl mcqResourceDtl = getOrCreateMCQResourceDtl(chapterId)
        if (!mcqResourceDtl) {
            return [status: "Error", message: "Failed to get or create MCQ resource"]
        }

        // Get total count of all questions for reporting
        int totalQuestions = ObjectiveMst.countByQuizId(new Integer(enhancedPYQsResource.resLink))

        if (totalQuestions == 0) {
            return [status: "Error", message: "No enhanced questions found"]
        }

        // Get count of unprocessed questions without loading them into memory
        int unprocessedCount = ObjectiveMst.createCriteria().count {
            eq('quizId', new Integer(enhancedPYQsResource.resLink))
            or {
                isNull('answer')
                eq('answer', '')
            }
        }

        if (unprocessedCount == 0) {
            return [
                status: "OK",
                message: "All questions already have solutions",
                totalQuestions: totalQuestions,
                unprocessedQuestions: 0,
                alreadyProcessed: totalQuestions
            ]
        }

        int alreadyProcessed = totalQuestions - unprocessedCount
        println("Found ${unprocessedCount} unprocessed questions out of ${totalQuestions} total questions (${alreadyProcessed} already processed)")

        // Process unprocessed questions in paginated batches to avoid memory issues
        def result = processEnhancedQuestionsWithPagination(enhancedPYQsResource, mcqResourceDtl, params, unprocessedCount)

        // Add summary information to result
        result.totalQuestions = totalQuestions
        result.unprocessedQuestions = unprocessedCount
        result.alreadyProcessed = alreadyProcessed

        // Step 1: Change resourceName from 'EnhancedPYQs' to 'QuestionBank QnA' in separate transaction
        if(result.status == "OK" || result.successfulBatches > 0) {
            updateResourceName(enhancedPYQsResource)
        }

        // Step 3: Check counts and delete empty resources in separate transaction
        cleanupEmptyResourcesWithTransaction(enhancedPYQsResource, mcqResourceDtl)

        return result
    }



    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private ResourceDtl getOrCreateMCQResourceDtl(String chapterId) {
        try {
            // First check if MCQ resource already exists
            ResourceDtl existingMcqResource = ResourceDtl.findByChapterIdAndResTypeAndResourceName(
                new Long(chapterId),
                "Multiple Choice Questions",
                "QuestionBank MCQs"
            )

            if (existingMcqResource) {
                println("Found existing MCQ resource with ID: ${existingMcqResource.resLink}")
                return existingMcqResource
            }

            // Create new MCQ resource if it doesn't exist
            println("Creating new MCQ resource for chapter: ${chapterId}")
            QuizIdGenerator mcqQuizIdGenerator = new QuizIdGenerator()
            mcqQuizIdGenerator.save()
            ResourceDtl mcqResourceDtl = new ResourceDtl(
                chapterId: new Long(chapterId),
                resType: "Multiple Choice Questions",
                resourceName: "QuestionBank MCQs",
                resLink: mcqQuizIdGenerator.id,
                createdBy: "System"
            )
            mcqResourceDtl.save(failOnError: true, flush: true)
            return mcqResourceDtl
        } catch (Exception e) {
            println("Error getting or creating MCQ resource: ${e.message}")
            return null
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void updateResourceName(ResourceDtl enhancedPYQsResource) {
        try {
            enhancedPYQsResource.resourceName = "QuestionBank QnA"
            enhancedPYQsResource.save(failOnError: true, flush: true)
        } catch (Exception e) {
            println("Error updating resource name: ${e.message}")
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void cleanupEmptyResourcesWithTransaction(ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl) {
        try {
            cleanupEmptyResources(qaResourceDtl, mcqResourceDtl)
        } catch (Exception e) {
            println("Error during cleanup: ${e.message}")
        }
    }



    private def processEnhancedQuestionsWithPagination(ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl, params, int totalUnprocessedCount) {
        int pageSize = 100  // Process 100 questions at a time to manage memory
        int noOfQuestionsPerIteration = QUESTIONS_PER_BATCH
        int maxParallelTasks = MAX_PARALLEL_TASKS
        int totalProcessed = 0
        int successfulBatches = 0
        int failedBatches = 0
        List<Map> failedBatchDetails = []
        List<Map> batchResults = []
        int currentOffset = 0

        println("Processing ${totalUnprocessedCount} unprocessed questions in pages of ${pageSize}")

        try {
            while (currentOffset < totalUnprocessedCount) {
                //store current time
                long startTime = System.currentTimeMillis()
                // Load a page of unprocessed questions
                List<ObjectiveMst> pageQuestions = ObjectiveMst.createCriteria().list(max: pageSize, offset: currentOffset) {
                    eq('quizId', new Integer(qaResourceDtl.resLink))
                    or {
                        isNull('answer')
                        eq('answer', '')
                    }
                    order('id', 'asc')  // Consistent ordering for pagination
                }

                if (pageQuestions.size() == 0) {
                    break  // No more questions to process
                }

                println("Processing page ${(currentOffset / pageSize) + 1}: ${pageQuestions.size()} questions (offset: ${currentOffset})")

                // Create batches from this page
                List<List<ObjectiveMst>> batches = []
                for (int i = 0; i < pageQuestions.size(); i += noOfQuestionsPerIteration) {
                    int endIndex = Math.min(i + noOfQuestionsPerIteration, pageQuestions.size())
                    batches.add(pageQuestions.subList(i, endIndex))
                }

                // Process batches in chunks of parallel tasks
                for (int i = 0; i < batches.size(); i += maxParallelTasks) {
                    int endIndex = Math.min(i + maxParallelTasks, batches.size())
                    List<List<ObjectiveMst>> currentChunk = batches.subList(i, endIndex)

                    println("Processing ${currentChunk.size()} solution batches in parallel (${i + 1}-${endIndex} of ${batches.size()}) from page ${(currentOffset / pageSize) + 1}")

                    // Create parallel tasks for this chunk
                    def promises = currentChunk.withIndex().collect { batch, batchIndex ->
                        task {
                            int globalBatchIndex = (currentOffset / pageSize) * (pageSize / noOfQuestionsPerIteration) + i + batchIndex
                            return [
                                batchIndex: globalBatchIndex,
                                llmResult: callLLMForEnhancedSolutions(params.serverIPAddress, params.chapterId, batch)
                            ]
                        }
                    }

                    // Wait for all tasks in this chunk to complete
                    def results = promises.collect { it.get() }

                    // Process results sequentially with individual batch transactions
                    results.each { result ->
                        if (result?.llmResult?.answer) {
                            try {
                                def batchResult = processEnhancedSolutionsResponseWithBatchTransaction(
                                    result.llmResult.answer,
                                    qaResourceDtl,
                                    mcqResourceDtl,
                                    result.batchIndex
                                )
                                batchResults.add(batchResult)

                                if (batchResult.status == "success") {
                                    successfulBatches++
                                    totalProcessed += batchResult.successfulUpdates
                                } else {
                                    failedBatches++
                                    failedBatchDetails.add([
                                        batchIndex: result.batchIndex,
                                        error: batchResult.error,
                                        failedQuestions: batchResult.failedQuestions
                                    ])
                                }
                            } catch (Exception e) {
                                failedBatches++
                                failedBatchDetails.add([
                                    batchIndex: result.batchIndex,
                                    error: "Exception processing batch: ${e.message}",
                                    failedQuestions: []
                                ])
                                println("Exception processing enhanced solutions batch ${result.batchIndex}: ${e.message}")
                            }
                        } else {
                            failedBatches++
                            failedBatchDetails.add([
                                batchIndex: result.batchIndex,
                                error: "No LLM response received",
                                failedQuestions: []
                            ])
                        }
                    }
                }

                currentOffset += pageQuestions.size()

                // Clear session periodically to prevent memory buildup
                ObjectiveMst.withSession { session ->
                    session.clear()
                }
                long endTime = System.currentTimeMillis()
                //show time taken for this page in minutes and seconds
                println("Time taken for page ${(currentOffset / pageSize) + 1}: ${((endTime - startTime) / 1000) / 60} minutes")
                println("the current offset is ${currentOffset} and the total unprocessed count is ${totalUnprocessedCount}")
            }
        } catch (Exception e) {
            println("Exception in paginated enhanced solutions processing: ${e.message}")
        }

        return [
            status: successfulBatches > 0 ? "OK" : "Error",
            totalProcessed: totalProcessed,
            successfulBatches: successfulBatches,
            failedBatches: failedBatches,
            totalBatches: successfulBatches + failedBatches,
            failedBatchDetails: failedBatchDetails,
            batchResults: batchResults
        ]
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private def callLLMForEnhancedSolutions(String serverIPAddress, String chapterId, List<ObjectiveMst> batch) {
        try {
            // Prepare questions for LLM
            String questionsText = batch.collect { question ->
                "Question ID: ${question.id}\nQuestion: ${question.question}\nAnswer: ${question.answer}"
            }.join("\n\n")

            // Get the solution prompt
            Prompts solutionPrompts = getCachedPrompt("solutionCreator")
            String prompt = solutionPrompts.basePrompt + "\n\nQuestions:\n" + questionsText

            // Call GPT for solutions
            def responseString = talkToGPT(prompt)
            return [answer: responseString?.response]
        } catch (Exception e) {
            println("Error calling LLM for enhanced solutions: ${e.message}")
            return [answer: null]
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private def processEnhancedSolutionsResponseWithBatchTransaction(String response, ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl, int batchIndex) {
        def result = [
            batchIndex: batchIndex,
            status: "success",
            successfulUpdates: 0,
            failedQuestions: [],
            error: null
        ]

        try {
            String cleanedResponse = jsonCleaner(response)
            def jsonResponse
            try {
                jsonResponse = new JsonSlurper().parseText(cleanedResponse)
            } catch (Exception e) {
                println("Failed to parse JSON for enhanced solutions batch ${batchIndex}: So calling fixJSONFromLLM")
                jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(cleanedResponse))
            }

            jsonResponse.each { solutionData ->
                try {
                    def updateResult = updateQuestionWithSolution(solutionData, qaResourceDtl, mcqResourceDtl)
                    if (updateResult.status == "success") {
                        result.successfulUpdates++
                    } else {
                        result.failedQuestions.add([
                            questionId: updateResult.questionId,
                            error: updateResult.error
                        ])
                    }
                } catch (Exception e) {
                    result.failedQuestions.add([
                        questionId: solutionData.questionNo ?: "unknown",
                        error: "Exception updating question: ${e.message}"
                    ])
                }
            }

            if (result.failedQuestions.size() > 0 && result.successfulUpdates == 0) {
                result.status = "failed"
                result.error = "All questions in batch failed to update"
            } else if (result.failedQuestions.size() > 0) {
                result.status = "partial"
                result.error = "Some questions in batch failed to update"
            }

        } catch (Exception e) {
            result.status = "failed"
            result.error = "Error processing batch response: ${e.message}"
            println("Error processing enhanced solutions response for batch ${batchIndex}: ${e.message}")
        }

        return result
    }

    private def updateQuestionWithSolution(Map solutionData, ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl) {
        try {
            ObjectiveMst originalQuestion = ObjectiveMst.findById(new Integer(solutionData.questionNo))
            if (!originalQuestion) {
                return [status: "error", questionId: solutionData.questionNo, error: "Question not found"]
            }

            if (solutionData.questionType == "MCQ") {
                // Move to MCQ resource and update format
                originalQuestion.quizId = new Integer(mcqResourceDtl.resLink)
                originalQuestion.quizType = "Multiple Choice Questions"
                originalQuestion.option1 = solutionData.option1
                originalQuestion.option2 = solutionData.option2
                originalQuestion.option3 = solutionData.option3
                originalQuestion.option4 = solutionData.option4
                originalQuestion.answer1 = solutionData.correctAnswer == "A" ? "Yes" : null
                originalQuestion.answer2 = solutionData.correctAnswer == "B" ? "Yes" : null
                originalQuestion.answer3 = solutionData.correctAnswer == "C" ? "Yes" : null
                originalQuestion.answer4 = solutionData.correctAnswer == "D" ? "Yes" : null
                originalQuestion.answerDescription = solutionData.explanation
            } else {
                // Keep in QA resource but update with solution
                originalQuestion.answer = solutionData.solution
                originalQuestion.answerDescription = solutionData.explanation
                originalQuestion.difficultylevel = solutionData.difficultyLevel ?: originalQuestion.difficultylevel
            }

            originalQuestion.save(failOnError: true, flush: true)
            return [status: "success", questionId: originalQuestion.id]

        } catch (Exception e) {
            println("Error updating question ${solutionData.questionNo}: ${e.message}")
            return [status: "error", questionId: solutionData.questionNo, error: e.message]
        }
    }

    private def processEnhancedSolutionsResponse(String response, ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl) {
        def results = [
            successful: [],
            failed: [],
            totalProcessed: 0
        ]

        try {
            String cleanedResponse = jsonCleaner(response)
            def jsonResponse
            try {
                jsonResponse = new JsonSlurper().parseText(cleanedResponse)
            } catch (Exception e) {
                println("Failed to parse JSON for enhanced solutions: So calling fixJSONFromLLM")
                jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(cleanedResponse))
            }

            jsonResponse.each { solutionData ->
                results.totalProcessed++
                try {
                    ObjectiveMst originalQuestion = ObjectiveMst.findById(new Integer(solutionData.questionNo))
                    if (originalQuestion) {
                        if (solutionData.questionType == "MCQ") {
                            // Move to MCQ resource and update format
                            originalQuestion.quizId = new Integer(mcqResourceDtl.resLink)
                            originalQuestion.quizType = "Multiple Choice Questions"
                            originalQuestion.option1 = solutionData.option1
                            originalQuestion.option2 = solutionData.option2
                            originalQuestion.option3 = solutionData.option3
                            originalQuestion.option4 = solutionData.option4
                            originalQuestion.answer1 = solutionData.correctAnswer == "A" ? "Yes" : null
                            originalQuestion.answer2 = solutionData.correctAnswer == "B" ? "Yes" : null
                            originalQuestion.answer3 = solutionData.correctAnswer == "C" ? "Yes" : null
                            originalQuestion.answer4 = solutionData.correctAnswer == "D" ? "Yes" : null
                            originalQuestion.answerDescription = solutionData.explanation
                        } else {
                            // Keep in QA resource but update with solution
                            originalQuestion.answer = solutionData.solution
                            originalQuestion.answerDescription = solutionData.explanation
                            originalQuestion.difficultylevel = solutionData.difficultyLevel ?: originalQuestion.difficultylevel
                        }
                        originalQuestion.save(failOnError: true, flush: true)
                        results.successful.add(originalQuestion.id)
                    } else {
                        results.failed.add([
                            questionId: solutionData.questionNo,
                            error: "Question not found"
                        ])
                    }
                } catch (Exception e) {
                    results.failed.add([
                        questionId: solutionData.questionNo ?: "unknown",
                        error: e.message
                    ])
                    println("Error processing question ${solutionData.questionNo}: ${e.message}")
                }
            }
        } catch (Exception e) {
            println("Error processing enhanced solutions response: ${e.message}")
            results.failed.add([error: "Response processing failed: ${e.message}"])
        }

        return results
    }

    def retryFailedBatches(params, List<Map> failedBatchDetails, ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl) {
        if (!failedBatchDetails || failedBatchDetails.size() == 0) {
            return [status: "No failed batches to retry", retriedBatches: 0, successfulRetries: 0]
        }

        int maxRetries = params.maxRetries ?: 2
        int retriedBatches = 0
        int successfulRetries = 0
        List<Map> stillFailedBatches = []

        println("Retrying ${failedBatchDetails.size()} failed batches with max ${maxRetries} retries each")

        failedBatchDetails.each { failedBatch ->
            boolean retrySuccessful = false
            int retryCount = 0

            while (!retrySuccessful && retryCount < maxRetries) {
                retryCount++
                retriedBatches++

                try {
                    println("Retry attempt ${retryCount}/${maxRetries} for batch ${failedBatch.batchIndex}")

                    // Get the original questions for this batch
                    List<ObjectiveMst> allEnhancedQuestions = ObjectiveMst.findAllByQuizId(new Integer(qaResourceDtl.resLink))
                    int startIndex = failedBatch.batchIndex * QUESTIONS_PER_BATCH
                    int endIndex = Math.min(startIndex + QUESTIONS_PER_BATCH, allEnhancedQuestions.size())

                    if (startIndex < allEnhancedQuestions.size()) {
                        List<ObjectiveMst> batchQuestions = allEnhancedQuestions.subList(startIndex, endIndex)

                        // Call LLM again for this batch
                        def llmResult = callLLMForEnhancedSolutions(params.serverIPAddress, params.chapterId, batchQuestions)

                        if (llmResult?.answer) {
                            // Process the response with batch transaction
                            def batchResult = processEnhancedSolutionsResponseWithBatchTransaction(
                                llmResult.answer,
                                qaResourceDtl,
                                mcqResourceDtl,
                                failedBatch.batchIndex
                            )

                            if (batchResult.status == "success") {
                                retrySuccessful = true
                                successfulRetries++
                                println("Retry successful for batch ${failedBatch.batchIndex} on attempt ${retryCount}")
                            } else {
                                println("Retry failed for batch ${failedBatch.batchIndex} on attempt ${retryCount}: ${batchResult.error}")
                            }
                        } else {
                            println("No LLM response received for retry of batch ${failedBatch.batchIndex} on attempt ${retryCount}")
                        }
                    } else {
                        println("Invalid batch index ${failedBatch.batchIndex} - skipping retry")
                        break
                    }
                } catch (Exception e) {
                    println("Exception during retry of batch ${failedBatch.batchIndex} on attempt ${retryCount}: ${e.message}")
                }
            }

            if (!retrySuccessful) {
                stillFailedBatches.add(failedBatch)
            }
        }

        return [
            status: "Retry completed",
            retriedBatches: retriedBatches,
            successfulRetries: successfulRetries,
            stillFailedBatches: stillFailedBatches,
            totalOriginalFailures: failedBatchDetails.size()
        ]
    }

    private def cleanupEmptyResources(ResourceDtl qaResourceDtl, ResourceDtl mcqResourceDtl) {
        // Check QA resource count
        int qaCount = ObjectiveMst.countByQuizId(new Integer(qaResourceDtl.resLink))
        if (qaCount == 0) {
            println("Deleting empty QA resource: ${qaResourceDtl.resourceName}")
            qaResourceDtl.delete(flush: true)
        }

        // Check MCQ resource count
        int mcqCount = ObjectiveMst.countByQuizId(new Integer(mcqResourceDtl.resLink))
        if (mcqCount == 0) {
            println("Deleting empty MCQ resource: ${mcqResourceDtl.resourceName}")
            mcqResourceDtl.delete(flush: true)
        }

        println("Final counts - QA: ${qaCount}, MCQ: ${mcqCount}")
    }

    @PreDestroy
    void cleanup() {
        try {
            executorService.shutdown()
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow()
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow()
            Thread.currentThread().interrupt()
        }

        try {
            httpClient.close()
        } catch (Exception e) {
            println("Error closing HTTP client: ${e.message}")
        }
    }
}
