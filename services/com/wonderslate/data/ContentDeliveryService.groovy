package com.wonderslate.data

import grails.transaction.Transactional

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import software.amazon.awssdk.services.cloudfront.CloudFrontUtilities;
import software.amazon.awssdk.services.cloudfront.model.CannedSignerRequest;
import software.amazon.awssdk.services.cloudfront.url.SignedUrl;
import java.security.MessageDigest;


@Transactional
class ContentDeliveryService {
    String keyPairId = "K1K3820LSJ1OXC" // CloudFront key pair ID
    String privateKeyPath = "supload/pk-APKA2OPFHBCSMBP7Q7RL.pem" // Path to your private key file
    def generateSignedURL(String resourceUrl, request = null) {
        try {
            CloudFrontUtilities cloudFrontUtilities = CloudFrontUtilities.create()
            // Short expiration (30 seconds)
            Instant expirationDate = Instant.now().plus(30, ChronoUnit.SECONDS)

            String site = null

            if (request != null) {
                println("SERVER URL: " + request.getServerName())
                String serverName = request.getServerName()
                site = serverName
            }

            String urlToSign = resourceUrl

            // Generate token using SHA256 hash of file path + expiration
            String tokenInput = resourceUrl + expirationDate.toEpochMilli()
            String token = generateSHA256Hash(tokenInput)

            // Save token in DB
            KeyValueMst keyValueMst = new KeyValueMst(
                    keyName: token,
                    keyValue: "false",
                    siteId: new Integer(-1)
            )
            keyValueMst.save(failOnError: true, flush: true)

            if (urlToSign.contains("?")) {
                urlToSign += "&token=${token}&site=${site}"
            } else {
                urlToSign += "?token=${token}&site=${site}"
            }

            // Build signed URL (now includes token & site)
            CannedSignerRequest cannedRequest = CannedSignerRequest.builder()
                    .resourceUrl(urlToSign)
                    .privateKey(new java.io.File(privateKeyPath).toPath())
                    .keyPairId(keyPairId)
                    .expirationDate(expirationDate)
                    .build()

            SignedUrl signedUrl = cloudFrontUtilities.getSignedUrlWithCannedPolicy(cannedRequest)
            println("Signed URL: ${signedUrl.url()}")

            return signedUrl.url()
        } catch (Exception e) {
            log.error("Error generating CloudFront signed URL: ${e.message}", e)
            throw e
        }
    }

    private String generateSHA256Hash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256")
            byte[] hash = digest.digest(input.getBytes("UTF-8"))
            StringBuilder hexString = new StringBuilder()

            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b)
                if (hex.length() == 1) {
                    hexString.append('0')
                }
                hexString.append(hex)
            }

            return hexString.toString()
        } catch (Exception e) {
            log.error("Error generating SHA256 hash: ${e.message}", e)
            throw e
        }
    }
}
