package com.wonderslate.shop

import com.wonderslate.data.BooksDtl
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import grails.gsp.PageRenderer
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.apache.pdfbox.multipdf.PDFMergerUtility
import org.apache.pdfbox.pdmodel.*
import org.apache.pdfbox.pdmodel.common.PDPageLabelRange
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.destination.PDPageDestination
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.destination.PDPageFitDestination
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.*
import java.util.Date;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Transactional
class PdfExporterService {

    PageRenderer groovyPageRenderer  // Automatically injected
    def wpmainService
    def dataProviderService
    def grailsApplication
    def redisService

    def generateTheoryBookPdf(String chapterId,String resourceId){
        try{
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(chapterId))
            File uploadDir = new File(grailsApplication.config.grails.basedir.path +"/supload/books/" + chaptersMst.bookId + "/chapters/" + chapterId + "/" + resourceId)
            if(!uploadDir.exists()) {
                println("dir doesn't exist, so creating")
                uploadDir.mkdirs()
            }
            String outputPath = ""+uploadDir.absolutePath+"/"+resourceId+".html"
            println("outputPath="+outputPath)
            renderTheoryPdfToHtmlFile(chaptersMst.name, outputPath,"/pdfExporter/theoryChapter",chapterId)
            String htmlPath = outputPath
            String pdfPath = ""+uploadDir.absolutePath+"/"+resourceId+".pdf"
            generatePdf(htmlPath, pdfPath)
        }catch (Exception e){
            println("Exception in generateTheoryBookPdf "+e.toString())
        }

    }
    def generateBookPdfFiles(String bookId){
        println("**** generateBookPdfFiles called with bookId "+bookId)
        String filePath = "Not created"
        try {
            File uploadDir =  new File(grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+bookId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            println("uploadDir="+uploadDir.absolutePath)
            String htmlPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+bookId+"/book_"+bookId+".html"
            String pdfPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+bookId+"/book_"+bookId+".pdf"
            BooksMst booksMst = dataProviderService.getBooksMst(new Integer(bookId))
            println("booksMst="+booksMst)
            def file = renderBookTitleHtml(
                    bookId,
                    htmlPath,"/pdfExporter/bookTitle"
            )

            println("htmlPath="+htmlPath)
            // Generate PDF
            boolean success = generatePdf(htmlPath, pdfPath)
            println("pdfPath="+pdfPath)
            String originalBookId = bookId
             if(booksMst.packageBookIds!=null) bookId = booksMst.packageBookIds

            List chaptersList = new ArrayList()
            String[] bookIds = bookId.split(",")
            for(int i=0;i<bookIds.length;i++){
                String tempBookId = bookIds[i]
                BooksDtl booksDtl = BooksDtl.findByBookId(new Integer(tempBookId))
                if(booksDtl!=null&&booksDtl.masterBookId!=null) tempBookId = ""+booksDtl.masterBookId
                chaptersList.addAll(ChaptersMst.findAllByBookId(new Integer(tempBookId)))
            }
            println("chaptersList="+chaptersList)
            chaptersList = chaptersList.findAll { chapter ->
                def chapterData = getChapterDataForPdf(chapter.id)
                chapterData.questionBank.qnaQuestions.size() > 0
            }
            //generate pdf for each chapter
            def chapters = []
            chaptersList.each{chapter->
                htmlPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+originalBookId+"/chapter_"+chapter.id+".html"
                 pdfPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+originalBookId+"/chapter_"+chapter.id+".pdf"
                 file = renderSampleToHtmlFile(
                        chapter.name,
                        htmlPath,"/pdfExporter/chapterPdf",""+chapter.id
                )
                chapters.add([file:"chapter_"+chapter.id+".pdf",title:chapter.name,id:chapter.id])
                generatePdf(htmlPath, pdfPath)

            }
             filePath = mergeBookWithChapters(originalBookId,chapters)

        } catch (Exception e) {
            log.error("Error generating chapter PDF: ${e.message}", e)

        }
        return filePath
    }
    def renderBookTitleHtml(String bookId, String outputPath,String view) {
        try {
            // Get book details using the same methods as aiBookDtl
            // Get book details using the same methods as aiBookDtl
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
            def model = [title: booksMst.title, bookId: bookId]
            // Get chapters list for book overview
            if (redisService.("chapters_" + bookId) == null) {
                dataProviderService.getChaptersList(new Long(bookId))
            }
            List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

            chaptersList = chaptersList.findAll { chapter ->
                def chapterData = getChapterDataForPdf(chapter.id)
                chapterData.questionBank.qnaQuestions.size() > 0
            }

            // Get book overview data for all chapters
            def bookOverviewData = wpmainService.getBookOverviewData(new Long(bookId), chaptersList)

            // Get book-level summary
            def bookLevelSummary = wpmainService.getBookLevelSummary(new Long(bookId), chaptersList)

            model.putAll([
                    booksMst: booksMst,
                    bookOverviewData: bookOverviewData,
                    bookLevelSummary: bookLevelSummary,
                    chaptersList: chaptersList
            ])
            String html = groovyPageRenderer.render(
                    view: view,
                    model: model
            )

            File outFile = new File(outputPath)
            outFile.text = html
            return "success"
        } catch (Exception e) {

           return "Failure"
        }
    }
    /**
     * Render the GSP and save to .html file
     */
    String    renderSampleToHtmlFile(String title, String outputPath,String view,String chapterId) {
        println("renderSampleToHtmlFile called with params: ${chapterId} view is "+view)

        def model = [title: title, chapterId: chapterId]

        // If this is for chapterPdf view, fetch all required data
            println("entered for chapterpdf ${chapterId}")
            Long chapterIdLong = Long.parseLong(chapterId)
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterIdLong)

            // Get all required data using existing WpmainService methods
            def exerciseSolutions = wpmainService.getExerciseSolutions(chapterIdLong)
            def questionBankData = wpmainService.getQuestionBankData(chapterIdLong)
            def questionTypeCounts = wpmainService.getQuestionTypeCounts(chapterIdLong)

            // Add chapter data to model
            model.putAll([
                chapterName: " ${chaptersMst.name}",
                exerciseSolutions: exerciseSolutions ?: [],
                questionBank: questionBankData ?: [:],
                questionTypeCounts: questionTypeCounts ?: [:]
            ])


        String html = groovyPageRenderer.render(
                view: view,
                model: model
        )

        File outFile = new File(outputPath)
        outFile.text = html
        return "success"
    }

    //String nodeBinary = "/Users/<USER>/.nvm/versions/node/v22.17.1/bin/node"  // or run `which node` to confirm
    //String puppeteerScript = "/Users/<USER>/Documents/Wonderslate/technical/puppeteer/puppeteer-test/render.js"

    /**
     * Calls Puppeteer to generate PDF from the given HTML file
     * @param htmlPath full path to .html file
     * @param pdfPath full path where PDF should be saved
     */
    boolean generatePdf(String htmlPath, String pdfPath) {
        println("generatePdf called with params: ${htmlPath} ${pdfPath}")
        String nodeBinary = grailsApplication.config.grails.nodebinary
        String puppeteerScript = grailsApplication.config.grails.puppeteerscript

		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
		String formattedDateTime = LocalDateTime.now().format(dtf);
        String userDataDirPath =  puppeteerScript.substring(0, puppeteerScript.lastIndexOf('/')+1) + formattedDateTime

        try {
			//creating userDataDirPath for chrome profile
            ProcessBuilder processBuilder = new ProcessBuilder("mkdir", "-p", userDataDirPath); 
            Process process = processBuilder.start();
            
			int exitCode = process.waitFor()
            if (exitCode != 0) {
                println("Folder creation failed: " + userDataDirPath)
                return false
            }

            processBuilder = new ProcessBuilder(
                    nodeBinary,
                    puppeteerScript,
                    htmlPath,
                    pdfPath,
					userDataDirPath
            )

            processBuilder.redirectErrorStream(true)
            process = processBuilder.start()

            // Optional: capture console output for logging
            def output = new StringBuilder()
            process.inputStream.eachLine { output.append(it).append("\n") }

            exitCode = process.waitFor()
            if (exitCode != 0) {
                println("Puppeteer failed: $output")
                return false
            }

            println("Puppeteer success: $output")
			
			//deleting userDataDirPath of chrome profile
            processBuilder = new ProcessBuilder("rm", "-rf", userDataDirPath); 
            process = processBuilder.start();
            
			exitCode = process.waitFor()
            if (exitCode != 0) {
                println("Folder deletion failed: " + userDataDirPath)
                return false
            }			
			
            return true

        } catch (Exception e) {
            println("Error running Puppeteer"+ e)
            return false
        }
    }

    /**
     * Get chapter data formatted for PDF export using existing WpmainService methods
     * @param chapterId the chapter ID
     * @return formatted chapter data
     */
    def getChapterDataForPdf(Long chapterId) {
        try {
            // Reuse the same methods that aibook uses
            def exerciseSolutions = wpmainService.getExerciseSolutions(chapterId)
            def questionBankData = wpmainService.getQuestionBankData(chapterId)
            def questionTypeCounts = wpmainService.getQuestionTypeCounts(chapterId)

            // Get chapter name from dataProviderService if needed
            def chapterName = "Chapter ${chapterId}" // Default fallback

            return [
                chapterName: chapterName,
                chapterId: chapterId,
                exerciseSolutions: exerciseSolutions ?: [],
                questionBank: questionBankData ?: [:],
                questionTypeCounts: questionTypeCounts ?: [:]
            ]
        } catch (Exception e) {
            log.error("Error getting chapter data for PDF: ${e.message}", e)
            return null
        }
    }

    /**
     * Render chapter to HTML file for PDF conversion
     * @param chapterId the chapter ID
     * @param outputPath path where HTML should be saved
     * @return File object of the generated HTML
     */
    String renderChapterToHtmlFile(Long chapterId, String outputPath) {
        String html = groovyPageRenderer.render(
                view: '/pdfExporter/chapterPdf',
                model: [chapterId: chapterId]
        )

        File outFile = new File(outputPath)
        outFile.text = html
        println("the file got created at "+outputPath)
        return "success"
    }

    /**
     * Merges book_<bookId>.pdf with chapter PDFs in order, adds bookmarks, and streams output
     * @param bookId e.g., "1234"
     * @param chapters List of [file: "chapter_01.pdf", title: "Light"]
     * @param response Grails response object (HttpServletResponse)
     */
    String mergeBookWithChapters(String bookId, List<Map> chapters) {
        println("mergeBookWithChapters called with bookId "+bookId)
        String basePath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/${bookId}"
        File outputFile = new File("${basePath}/Complete_${bookId}.pdf")

        PDDocument mergedDoc = new PDDocument()
        PDDocumentOutline outline = new PDDocumentOutline()
        mergedDoc.getDocumentCatalog().setDocumentOutline(outline)
        boolean hasTheory = false;
        BooksDtl booksDtl = BooksDtl.findByBookId(new Long(bookId))
        if(booksDtl!=null&&booksDtl.syllabusSubject!=null) hasTheory=true;
        println("hasTheory="+hasTheory)

        int pageOffset = 0

        // Load and append cover/book file first
        File bookFile = new File("${basePath}/book_${bookId}.pdf")
        if (!bookFile.exists()) {
            throw new FileNotFoundException("Missing main book file: ${bookFile.absolutePath}")
        }

        PDDocument bookDoc = PDDocument.load(bookFile)
        appendDocumentWithBookmark(mergedDoc, bookDoc, "Cover & TOC", pageOffset, outline)
        pageOffset += bookDoc.getNumberOfPages()
        bookDoc.close()

        // Append all chapters with bookmarks
        chapters.each { chapter ->
            //if the theory book, then we have to add theory pdf first. It is available in the resLink of ResourceDtl with the given chatperId and resType='Notes'
            if(hasTheory) {
                println("Processing theory PDF for chapter: ${chapter.id}")
                ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapter.id), "Notes")
                println("resourceDtl found: ${resourceDtl != null}")
                File theoryFile
                if (resourceDtl != null) {
                    println("resourceDtl file path="+resourceDtl.resLink)
                     theoryFile = new File(grailsApplication.config.grails.basedir.path + "/" + resourceDtl.resLink)
                    println("theoryFile path: ${theoryFile.absolutePath}")
                    println("theoryFile exists: ${theoryFile.exists()}")
                    if (theoryFile.exists()) {
                        println("Loading theory PDF for chapter: ${chapter.title}")
                        try {
                            PDDocument theoryDoc = PDDocument.load(theoryFile)
                            println("Theory PDF loaded successfully. Pages: ${theoryDoc.getNumberOfPages()}")
                            println("Current pageOffset before theory: ${pageOffset}")
                            appendDocumentWithBookmark(mergedDoc, theoryDoc, "Theory - " + chapter.title, pageOffset, outline)
                            pageOffset += theoryDoc.getNumberOfPages()
                            println("pageOffset after theory: ${pageOffset}")
                            theoryDoc.close()
                        } catch (Exception e) {
                            println("Error loading theory PDF: ${e.message}")
                            e.printStackTrace()
                        }
                    } else {
                        println("Theory file does not exist at path: ${theoryFile.absolutePath}")
                    }
                } else {
                    println("No ResourceDtl found for chapterId: ${chapter.id} and resType: 'Notes'")
                }
            }


            String filename = chapter.file
            String title = chapter.title
            if(hasTheory) title = "Exercise - "+title

            File chapterFile = new File("${basePath}/${filename}")
            if (!chapterFile.exists()) {
                throw new FileNotFoundException("Missing chapter file: ${chapterFile.absolutePath}")
            }

            PDDocument chapterDoc = PDDocument.load(chapterFile)

           appendDocumentWithBookmark(mergedDoc, chapterDoc, title, pageOffset, outline)
            pageOffset += chapterDoc.getNumberOfPages()
            chapterDoc.close()
        }

        outline.openNode()

        // Save the final merged PDF (overwrite)
        println("Final merged PDF will be saved with ${mergedDoc.getNumberOfPages()} total pages")
        mergedDoc.save(outputFile)
        mergedDoc.close()
        println("booksDtl="+booksDtl)
        if(booksDtl==null){
            booksDtl = new BooksDtl(bookId:new Integer(bookId))
        }
        booksDtl.pdfPath = "supload/bookspdf/${bookId}/Complete_${bookId}.pdf"
        booksDtl.save(flush: true, failOnError: true)
        println("booksDtl.pdfPath="+booksDtl.pdfPath)
        // Stream the file to controller response
        return booksDtl.pdfPath
    }

    private void appendDocumentWithBookmark(PDDocument mergedDoc, PDDocument toAppend, String title, int pageOffset, PDDocumentOutline outline) {
        println("appendDocumentWithBookmark called with title: ${title}, pageOffset: ${pageOffset}")
        println("mergedDoc pages before append: ${mergedDoc.getNumberOfPages()}")
        println("toAppend pages: ${toAppend.getNumberOfPages()}")

        // Store the current page count before appending
        int currentPageCount = mergedDoc.getNumberOfPages()

        PDFMergerUtility merger = new PDFMergerUtility()
        merger.appendDocument(mergedDoc, toAppend)

        println("mergedDoc pages after append: ${mergedDoc.getNumberOfPages()}")

        PDOutlineItem bookmark = new PDOutlineItem()
        bookmark.setTitle(title)

        if (toAppend.getNumberOfPages() > 0) {
            try {
                PDPageDestination dest = new PDPageFitDestination()
                // The pageOffset should match the current page count before append
                // because appendDocument adds pages at the end
                if (pageOffset == currentPageCount) {
                    dest.setPage(mergedDoc.getPage(pageOffset))
                } else {
                    // If pageOffset doesn't match currentPageCount, use currentPageCount
                    println("Warning: pageOffset (${pageOffset}) doesn't match currentPageCount (${currentPageCount}), using currentPageCount")
                    dest.setPage(mergedDoc.getPage(currentPageCount))
                }
                bookmark.setDestination(dest)
                outline.addLast(bookmark)
                println("Successfully added bookmark for: ${title} at page index: ${pageOffset}")
            } catch (Exception e) {
                println("Error creating bookmark for ${title}: ${e.message}")
                e.printStackTrace()
            }
        }
    }


    String  renderTheoryPdfToHtmlFile(String title, String outputPath,String view,String chapterId) {
        println("renderTheoryPdfToHtmlFile called with params: ${chapterId} view is "+view)

        def model = [title: title, chapterId: chapterId]

        // If this is for chapterPdf view, fetch all required data
        println("entered for theoryChapter ${chapterId}")
        Long chapterIdLong = Long.parseLong(chapterId)
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterIdLong)

        String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"

        // Read the HTML content
        File chapterFile = new File(chapterFilePath)
        String chapterContent = ""
        if (chapterFile.exists()) {
            chapterContent = chapterFile.text
        }
        println("chapterContent="+chapterContent)

        // Add chapter data to model
        model.putAll([
                chaptersMst: chaptersMst,
                chapterContent: chapterContent,
                chapterFilePath: chapterFilePath
        ])


        String html = groovyPageRenderer.render(
                view: view,
                model: model
        )

        File outFile = new File(outputPath)
        outFile.text = html
        return "success"
    }

}
