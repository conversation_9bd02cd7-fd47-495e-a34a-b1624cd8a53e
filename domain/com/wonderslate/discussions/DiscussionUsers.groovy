package com.wonderslate.discussions

import org.bson.types.ObjectId

class DiscussionUsers implements Serializable{
    static mapWith = "mongo"

    ObjectId id
    String userid
    String userName
    List questionIds
    List answeredIds
    List questionsFollowing
    List questionVoted
    List answerVoted
    Integer totalUpVoteCount

    static mapping = {
        userid index:true
    }

    static embedded = ['questionIds', 'answeredQuestionIds', 'questionsFollowing']
}
